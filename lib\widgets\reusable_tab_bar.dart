import 'package:flutter/material.dart';

/// Tab item configuration
class TabItem {
  final String label;
  final IconData icon;
  final Color? color;

  const TabItem({
    required this.label,
    required this.icon,
    this.color,
  });
}

/// Tab configurations for common patterns
class TabConfigurations {
  /// Three tab module configuration
  static List<TabItem> threeTabModule({
    required String tab1Label,
    required IconData tab1Icon,
    required String tab2Label,
    required IconData tab2Icon,
    required String tab3Label,
    required IconData tab3Icon,
    Color? tab1Color,
    Color? tab2Color,
    Color? tab3Color,
  }) {
    return [
      TabItem(label: tab1Label, icon: tab1Icon, color: tab1Color),
      TabItem(label: tab2Label, icon: tab2Icon, color: tab2Color),
      TabItem(label: tab3Label, icon: tab3Icon, color: tab3Color),
    ];
  }

  /// Two tab detail configuration
  static List<TabItem> twoTabDetail({
    required String tab1Label,
    required IconData tab1Icon,
    required String tab2Label,
    required IconData tab2Icon,
    Color? tab1Color,
    Color? tab2Color,
  }) {
    return [
      TabItem(label: tab1Label, icon: tab1Icon, color: tab1Color),
      TabItem(label: tab2Label, icon: tab2Icon, color: tab2Color),
    ];
  }

  /// Four tab module configuration
  static List<TabItem> fourTabModule({
    required String tab1Label,
    required IconData tab1Icon,
    required String tab2Label,
    required IconData tab2Icon,
    required String tab3Label,
    required IconData tab3Icon,
    required String tab4Label,
    required IconData tab4Icon,
    Color? tab1Color,
    Color? tab2Color,
    Color? tab3Color,
    Color? tab4Color,
  }) {
    return [
      TabItem(label: tab1Label, icon: tab1Icon, color: tab1Color),
      TabItem(label: tab2Label, icon: tab2Icon, color: tab2Color),
      TabItem(label: tab3Label, icon: tab3Icon, color: tab3Color),
      TabItem(label: tab4Label, icon: tab4Icon, color: tab4Color),
    ];
  }
}

/// Reusable Tab Bar Widget with multicolor support
class ReusableTabBar extends StatelessWidget {
  final List<TabItem> tabs;
  final TabController? controller;
  final Color? indicatorColor;
  final Color? labelColor;
  final Color? unselectedLabelColor;
  final bool isScrollable;
  final bool useMulticolor;

  const ReusableTabBar({
    super.key,
    required this.tabs,
    this.controller,
    this.indicatorColor,
    this.labelColor,
    this.unselectedLabelColor,
    this.isScrollable = false,
    this.useMulticolor = false,
  });

  /// Factory constructor for controlled tab bar
  factory ReusableTabBar.controlled({
    required TabController controller,
    required List<TabItem> tabs,
    Color? indicatorColor,
    Color? labelColor,
    Color? unselectedLabelColor,
    bool isScrollable = false,
    bool useMulticolor = false,
  }) {
    return ReusableTabBar(
      tabs: tabs,
      controller: controller,
      indicatorColor: indicatorColor,
      labelColor: labelColor,
      unselectedLabelColor: unselectedLabelColor,
      isScrollable: isScrollable,
      useMulticolor: useMulticolor,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (useMulticolor && controller != null) {
      // Use AnimatedBuilder for multicolor tabs
      return AnimatedBuilder(
        animation: controller!,
        builder: (context, child) {
          return TabBar(
            controller: controller,
            isScrollable: isScrollable,
            indicatorColor: indicatorColor ?? theme.primaryColor,
            labelColor: null, // We'll handle colors per tab
            unselectedLabelColor: null, // We'll handle colors per tab
            tabs: tabs.asMap().entries.map((entry) {
              final index = entry.key;
              final tabItem = entry.value;
              final isActive = controller!.index == index;
              final tabColor = tabItem.color ?? theme.primaryColor;
              final activeColor = tabColor;
              final inactiveColor = tabColor.withOpacity(0.4); // Light shade

              return Tab(
                icon: Icon(
                  tabItem.icon,
                  color: isActive ? activeColor : inactiveColor,
                ),
                child: Text(
                  tabItem.label,
                  style: TextStyle(
                    color: isActive ? activeColor : inactiveColor,
                    fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              );
            }).toList(),
          );
        },
      );
    } else {
      // Standard single-color tabs
      return TabBar(
        controller: controller,
        isScrollable: isScrollable,
        indicatorColor: indicatorColor ?? theme.primaryColor,
        labelColor: labelColor,
        unselectedLabelColor: unselectedLabelColor,
        tabs: tabs.map((tabItem) {
          return Tab(
            icon: Icon(
              tabItem.icon,
              color: tabItem.color,
            ),
            text: tabItem.label,
          );
        }).toList(),
      );
    }
  }
}
