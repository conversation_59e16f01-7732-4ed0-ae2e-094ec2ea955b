import 'dart:async';
import 'package:logging/logging.dart';
import 'package:uuid/uuid.dart';

/// Universal Service Layer Patterns
/// 
/// Provides standardized service layer patterns with:
/// - Consistent CRUD operations
/// - Standardized error handling
/// - Logging and monitoring
/// - Result types for better error management
/// - Validation patterns
/// - Caching strategies

/// Result type for service operations
sealed class ServiceResult<T, E> {
  const ServiceResult();
}

/// Success result
class Success<T, E> extends ServiceResult<T, E> {
  final T data;
  const Success(this.data);
}

/// Error result
class Failure<T, E> extends ServiceResult<T, E> {
  final E error;
  final String? message;
  final StackTrace? stackTrace;
  
  const Failure(this.error, {this.message, this.stackTrace});
}

/// Service error types
enum ServiceErrorType {
  validation,
  notFound,
  duplicate,
  database,
  network,
  permission,
  unknown,
}

/// Service error class
class ServiceError {
  final ServiceErrorType type;
  final String message;
  final String? code;
  final Map<String, dynamic>? details;
  final Exception? originalException;

  const ServiceError({
    required this.type,
    required this.message,
    this.code,
    this.details,
    this.originalException,
  });

  factory ServiceError.validation(String message, {String? code, Map<String, dynamic>? details}) {
    return ServiceError(
      type: ServiceErrorType.validation,
      message: message,
      code: code,
      details: details,
    );
  }

  factory ServiceError.notFound(String message, {String? code}) {
    return ServiceError(
      type: ServiceErrorType.notFound,
      message: message,
      code: code,
    );
  }

  factory ServiceError.duplicate(String message, {String? code}) {
    return ServiceError(
      type: ServiceErrorType.duplicate,
      message: message,
      code: code,
    );
  }

  factory ServiceError.database(String message, {Exception? exception}) {
    return ServiceError(
      type: ServiceErrorType.database,
      message: message,
      originalException: exception,
    );
  }

  factory ServiceError.network(String message, {Exception? exception}) {
    return ServiceError(
      type: ServiceErrorType.network,
      message: message,
      originalException: exception,
    );
  }

  factory ServiceError.permission(String message) {
    return ServiceError(
      type: ServiceErrorType.permission,
      message: message,
    );
  }

  factory ServiceError.unknown(String message, {Exception? exception}) {
    return ServiceError(
      type: ServiceErrorType.unknown,
      message: message,
      originalException: exception,
    );
  }

  @override
  String toString() => 'ServiceError(${type.name}): $message';
}

/// Universal Service Base Class
/// 
/// Provides common service functionality that all services can inherit from.
abstract class UniversalService<T> {
  late final Logger _logger;
  late final Uuid _uuid;
  final Map<String, T> _cache = {};
  Timer? _cacheCleanupTimer;

  UniversalService(String serviceName) {
    _logger = Logger(serviceName);
    _uuid = const Uuid();
    _setupCacheCleanup();
  }

  /// Service name for logging and identification
  String get serviceName;

  /// Cache duration (override in subclasses)
  Duration get cacheDuration => const Duration(minutes: 5);

  /// Whether caching is enabled (override in subclasses)
  bool get isCachingEnabled => true;

  /// Setup periodic cache cleanup
  void _setupCacheCleanup() {
    _cacheCleanupTimer = Timer.periodic(cacheDuration, (_) {
      _cleanupCache();
    });
  }

  /// Clean up expired cache entries
  void _cleanupCache() {
    // Simple cache cleanup - in a real implementation, you'd track timestamps
    if (_cache.length > 100) {
      _cache.clear();
      _logger.info('Cache cleared due to size limit');
    }
  }

  /// Generate unique business ID
  String generateBusinessId([String? prefix]) {
    final id = _uuid.v4();
    return prefix != null ? '$prefix-$id' : id;
  }

  /// Log service operation
  void logOperation(String operation, {Map<String, dynamic>? details}) {
    _logger.info('$serviceName: $operation', details);
  }

  /// Log service error
  void logError(String operation, dynamic error, {StackTrace? stackTrace}) {
    _logger.severe('$serviceName: $operation failed', error, stackTrace);
  }

  /// Execute operation with error handling and logging
  Future<ServiceResult<R, ServiceError>> executeOperation<R>(
    String operationName,
    Future<R> Function() operation, {
    Map<String, dynamic>? context,
  }) async {
    try {
      logOperation(operationName, details: context);
      final result = await operation();
      logOperation('$operationName completed successfully');
      return Success(result);
    } catch (e, stackTrace) {
      logError(operationName, e, stackTrace: stackTrace);
      
      // Convert exception to ServiceError
      final serviceError = _convertExceptionToServiceError(e);
      return Failure(serviceError, message: serviceError.message, stackTrace: stackTrace);
    }
  }

  /// Convert exception to ServiceError
  ServiceError _convertExceptionToServiceError(dynamic exception) {
    if (exception is ServiceError) {
      return exception;
    }
    
    final message = exception.toString();
    
    // Pattern matching for common error types
    if (message.contains('validation') || message.contains('invalid')) {
      return ServiceError.validation(message);
    } else if (message.contains('not found') || message.contains('does not exist')) {
      return ServiceError.notFound(message);
    } else if (message.contains('duplicate') || message.contains('already exists')) {
      return ServiceError.duplicate(message);
    } else if (message.contains('database') || message.contains('sql')) {
      return ServiceError.database(message, exception: exception);
    } else if (message.contains('network') || message.contains('connection')) {
      return ServiceError.network(message, exception: exception);
    } else if (message.contains('permission') || message.contains('unauthorized')) {
      return ServiceError.permission(message);
    } else {
      return ServiceError.unknown(message, exception: exception);
    }
  }

  /// Cache operations
  void cacheItem(String key, T item) {
    if (isCachingEnabled) {
      _cache[key] = item;
    }
  }

  T? getCachedItem(String key) {
    return isCachingEnabled ? _cache[key] : null;
  }

  void clearCache() {
    _cache.clear();
    logOperation('Cache cleared');
  }

  void removeCachedItem(String key) {
    _cache.remove(key);
  }

  /// Dispose resources
  void dispose() {
    _cacheCleanupTimer?.cancel();
    _cache.clear();
  }
}

/// CRUD Service Mixin
/// 
/// Provides standard CRUD operation patterns.
mixin CrudServiceMixin<T> on UniversalService<T> {
  /// Create operation
  Future<ServiceResult<String, ServiceError>> create(T item) async {
    return executeOperation('create', () async {
      final id = await performCreate(item);
      if (isCachingEnabled && id.isNotEmpty) {
        cacheItem(id, item);
      }
      return id;
    });
  }

  /// Read operation
  Future<ServiceResult<T?, ServiceError>> read(String id) async {
    return executeOperation('read', () async {
      // Check cache first
      final cached = getCachedItem(id);
      if (cached != null) {
        logOperation('Cache hit for read operation', details: {'id': id});
        return cached;
      }

      final item = await performRead(id);
      if (item != null && isCachingEnabled) {
        cacheItem(id, item);
      }
      return item;
    });
  }

  /// Update operation
  Future<ServiceResult<void, ServiceError>> update(String id, T item) async {
    return executeOperation('update', () async {
      await performUpdate(id, item);
      if (isCachingEnabled) {
        cacheItem(id, item);
      }
    });
  }

  /// Delete operation
  Future<ServiceResult<void, ServiceError>> delete(String id) async {
    return executeOperation('delete', () async {
      await performDelete(id);
      if (isCachingEnabled) {
        removeCachedItem(id);
      }
    });
  }

  /// List operation
  Future<ServiceResult<List<T>, ServiceError>> list({
    int? limit,
    int? offset,
    Map<String, dynamic>? filters,
  }) async {
    return executeOperation('list', () async {
      return await performList(limit: limit, offset: offset, filters: filters);
    }, context: {'limit': limit, 'offset': offset, 'filters': filters});
  }

  /// Abstract methods to be implemented by concrete services
  Future<String> performCreate(T item);
  Future<T?> performRead(String id);
  Future<void> performUpdate(String id, T item);
  Future<void> performDelete(String id);
  Future<List<T>> performList({int? limit, int? offset, Map<String, dynamic>? filters});
}

/// Validation Service Mixin
/// 
/// Provides validation patterns for service operations.
mixin ValidationServiceMixin<T> on UniversalService<T> {
  /// Validate item before operations
  Future<ServiceResult<void, ServiceError>> validate(T item, {bool isUpdate = false}) async {
    return executeOperation('validate', () async {
      final errors = await performValidation(item, isUpdate: isUpdate);
      if (errors.isNotEmpty) {
        throw ServiceError.validation(
          'Validation failed',
          details: {'errors': errors},
        );
      }
    });
  }

  /// Abstract validation method
  Future<List<String>> performValidation(T item, {bool isUpdate = false});
}

/// Extension methods for ServiceResult
extension ServiceResultExtensions<T, E> on ServiceResult<T, E> {
  /// Check if result is success
  bool get isSuccess => this is Success<T, E>;

  /// Check if result is failure
  bool get isFailure => this is Failure<T, E>;

  /// Get data if success, null otherwise
  T? get data => isSuccess ? (this as Success<T, E>).data : null;

  /// Get error if failure, null otherwise
  E? get error => isFailure ? (this as Failure<T, E>).error : null;

  /// Get error message if failure, null otherwise
  String? get errorMessage => isFailure ? (this as Failure<T, E>).message : null;

  /// Transform success data
  ServiceResult<R, E> map<R>(R Function(T) transform) {
    return switch (this) {
      Success(data: final data) => Success(transform(data)),
      Failure(error: final error, message: final message, stackTrace: final stackTrace) => 
        Failure(error, message: message, stackTrace: stackTrace),
    };
  }

  /// Handle both success and failure cases
  R fold<R>(
    R Function(T) onSuccess,
    R Function(E) onFailure,
  ) {
    return switch (this) {
      Success(data: final data) => onSuccess(data),
      Failure(error: final error) => onFailure(error),
    };
  }
}
