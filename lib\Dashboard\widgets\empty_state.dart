import 'package:flutter/material.dart';

/// Universal Empty State System
///
/// Provides consistent empty state displays across all modules with:
/// - Module-specific theming and colors
/// - Multiple empty state types (no data, filtered, network, etc.)
/// - Consistent styling and animations
/// - Contextual actions and buttons
/// - Support for different layouts (center, card, inline)

/// Empty state types for different scenarios
enum EmptyStateType {
  noData,        // No data available at all
  noResults,     // No results from search/filter
  noNetwork,     // Network connectivity issues
  noPermission,  // Permission denied
  error,         // General error state
  maintenance,   // Under maintenance
  custom,        // Custom empty state
}

/// Empty state layouts for different contexts
enum EmptyStateLayout {
  center,        // Centered in available space
  card,          // Card-style with background
  inline,        // Inline with minimal padding
  chart,         // Specialized for charts
}

/// Module themes for consistent colors and styling
class UniversalEmptyStateTheme {
  // Module-specific primary colors matching UniversalRecordTheme
  static const Color weight = Color(0xFF1976D2);       // Blue
  static const Color health = Color(0xFF00796B);       // Teal
  static const Color milk = Color(0xFF2E7D32);         // Green
  static const Color breeding = Color(0xFFD32F2F);     // Red
  static const Color events = Color(0xFF7B1FA2);       // Purple
  static const Color pregnancy = Color(0xFF9C27B0);    // Purple (distinct)
  static const Color delivery = Color(0xFF2E7D32);     // Green
  static const Color transactions = Color(0xFFFF8F00); // Orange
  static const Color cattle = Color(0xFF5D4037);       // Brown
  static const Color farmSetup = Color(0xFF455A64);    // Blue Grey
  static const Color reports = Color(0xFF6A1B9A);      // Deep Purple
  static const Color notifications = Color(0xFFE65100); // Deep Orange

  // Default colors for different states
  static const Color defaultGrey = Color(0xFF9E9E9E);
  static const Color errorRed = Color(0xFFD32F2F);
  static const Color warningOrange = Color(0xFFFF8F00);
  static const Color successGreen = Color(0xFF2E7D32);
}

/// Universal Empty State Widget
///
/// Replaces all existing empty state implementations with a single,
/// comprehensive widget that handles all use cases across modules.
class UniversalEmptyState extends StatelessWidget {
  final String title;
  final String message;
  final IconData icon;
  final Color? color;
  final Widget? action;
  final EmptyStateType type;
  final EmptyStateLayout layout;
  final double? height;
  final bool animated;
  final EdgeInsets? padding;

  const UniversalEmptyState({
    super.key,
    required this.title,
    required this.message,
    this.icon = Icons.inbox_outlined,
    this.color,
    this.action,
    this.type = EmptyStateType.noData,
    this.layout = EmptyStateLayout.center,
    this.height,
    this.animated = true,
    this.padding,
  });

  /// Factory constructors for common scenarios

  /// No data available (first time, empty database)
  factory UniversalEmptyState.noData({
    required String title,
    required String message,
    IconData? icon,
    Color? color,
    Widget? action,
    EmptyStateLayout layout = EmptyStateLayout.center,
  }) {
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon ?? Icons.inbox_outlined,
      color: color ?? UniversalEmptyStateTheme.defaultGrey,
      action: action,
      type: EmptyStateType.noData,
      layout: layout,
    );
  }

  /// No results from search or filters
  factory UniversalEmptyState.noResults({
    required String title,
    required String message,
    IconData? icon,
    Color? color,
    Widget? action,
    EmptyStateLayout layout = EmptyStateLayout.center,
  }) {
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon ?? Icons.filter_alt_off_outlined,
      color: color ?? UniversalEmptyStateTheme.defaultGrey,
      action: action,
      type: EmptyStateType.noResults,
      layout: layout,
    );
  }

  /// Network connectivity issues
  factory UniversalEmptyState.noNetwork({
    required String title,
    required String message,
    IconData? icon,
    Color? color,
    Widget? action,
    EmptyStateLayout layout = EmptyStateLayout.center,
  }) {
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon ?? Icons.wifi_off_outlined,
      color: color ?? UniversalEmptyStateTheme.warningOrange,
      action: action,
      type: EmptyStateType.noNetwork,
      layout: layout,
    );
  }

  /// Error state
  factory UniversalEmptyState.error({
    required String title,
    required String message,
    IconData? icon,
    Color? color,
    Widget? action,
    EmptyStateLayout layout = EmptyStateLayout.center,
  }) {
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon ?? Icons.error_outline,
      color: color ?? UniversalEmptyStateTheme.errorRed,
      action: action,
      type: EmptyStateType.error,
      layout: layout,
    );
  }

  /// Module-specific factory constructors

  /// Weight module empty state
  factory UniversalEmptyState.weight({
    required String title,
    required String message,
    IconData? icon,
    Widget? action,
    EmptyStateType type = EmptyStateType.noData,
    EmptyStateLayout layout = EmptyStateLayout.center,
  }) {
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon ?? Icons.monitor_weight,
      color: UniversalEmptyStateTheme.weight,
      action: action,
      type: type,
      layout: layout,
    );
  }

  /// Health module empty state
  factory UniversalEmptyState.health({
    required String title,
    required String message,
    IconData? icon,
    Widget? action,
    EmptyStateType type = EmptyStateType.noData,
    EmptyStateLayout layout = EmptyStateLayout.center,
  }) {
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon ?? Icons.health_and_safety_outlined,
      color: UniversalEmptyStateTheme.health,
      action: action,
      type: type,
      layout: layout,
    );
  }

  /// Milk module empty state
  factory UniversalEmptyState.milk({
    required String title,
    required String message,
    IconData? icon,
    Widget? action,
    EmptyStateType type = EmptyStateType.noData,
    EmptyStateLayout layout = EmptyStateLayout.center,
  }) {
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon ?? Icons.water_drop_outlined,
      color: UniversalEmptyStateTheme.milk,
      action: action,
      type: type,
      layout: layout,
    );
  }

  /// Breeding module empty state
  factory UniversalEmptyState.breeding({
    required String title,
    required String message,
    IconData? icon,
    Widget? action,
    EmptyStateType type = EmptyStateType.noData,
    EmptyStateLayout layout = EmptyStateLayout.center,
  }) {
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon ?? Icons.favorite_outline,
      color: UniversalEmptyStateTheme.breeding,
      action: action,
      type: type,
      layout: layout,
    );
  }

  /// Events module empty state
  factory UniversalEmptyState.events({
    required String title,
    required String message,
    IconData? icon,
    Widget? action,
    EmptyStateType type = EmptyStateType.noData,
    EmptyStateLayout layout = EmptyStateLayout.center,
  }) {
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon ?? Icons.event_outlined,
      color: UniversalEmptyStateTheme.events,
      action: action,
      type: type,
      layout: layout,
    );
  }

  /// Transactions module empty state
  factory UniversalEmptyState.transactions({
    required String title,
    required String message,
    IconData? icon,
    Widget? action,
    EmptyStateType type = EmptyStateType.noData,
    EmptyStateLayout layout = EmptyStateLayout.center,
  }) {
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon ?? Icons.account_balance_wallet_outlined,
      color: UniversalEmptyStateTheme.transactions,
      action: action,
      type: type,
      layout: layout,
    );
  }

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? _getDefaultColorForType();

    switch (layout) {
      case EmptyStateLayout.center:
        return _buildCenterLayout(context, effectiveColor);
      case EmptyStateLayout.card:
        return _buildCardLayout(context, effectiveColor);
      case EmptyStateLayout.inline:
        return _buildInlineLayout(context, effectiveColor);
      case EmptyStateLayout.chart:
        return _buildChartLayout(context, effectiveColor);
    }
  }

  /// Get default color based on empty state type
  Color _getDefaultColorForType() {
    switch (type) {
      case EmptyStateType.noData:
        return UniversalEmptyStateTheme.defaultGrey;
      case EmptyStateType.noResults:
        return UniversalEmptyStateTheme.defaultGrey;
      case EmptyStateType.noNetwork:
        return UniversalEmptyStateTheme.warningOrange;
      case EmptyStateType.noPermission:
        return UniversalEmptyStateTheme.errorRed;
      case EmptyStateType.error:
        return UniversalEmptyStateTheme.errorRed;
      case EmptyStateType.maintenance:
        return UniversalEmptyStateTheme.warningOrange;
      case EmptyStateType.custom:
        return UniversalEmptyStateTheme.defaultGrey;
    }
  }

  /// Build center layout (default, replaces current EmptyState)
  Widget _buildCenterLayout(BuildContext context, Color effectiveColor) {
    final content = _buildContent(context, effectiveColor, isCenter: true);

    return Center(
      child: Padding(
        padding: padding ?? const EdgeInsets.all(32.0),
        child: animated
          ? _buildAnimatedContent(content)
          : content,
      ),
    );
  }

  /// Build card layout (replaces MilkEmptyState style)
  Widget _buildCardLayout(BuildContext context, Color effectiveColor) {
    final content = _buildContent(context, effectiveColor, isCenter: false);

    return Container(
      width: double.infinity,
      padding: padding ?? const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: animated
        ? _buildAnimatedContent(content)
        : content,
    );
  }

  /// Build inline layout (minimal padding)
  Widget _buildInlineLayout(BuildContext context, Color effectiveColor) {
    final content = _buildContent(context, effectiveColor, isCenter: false);

    return Padding(
      padding: padding ?? const EdgeInsets.all(16.0),
      child: animated
        ? _buildAnimatedContent(content)
        : content,
    );
  }

  /// Build chart layout (replaces ChartEmptyState)
  Widget _buildChartLayout(BuildContext context, Color effectiveColor) {
    final content = _buildContent(context, effectiveColor, isCenter: false, isChart: true);

    return Container(
      height: height ?? 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Padding(
          padding: padding ?? const EdgeInsets.all(24.0),
          child: animated
            ? _buildAnimatedContent(content)
            : content,
        ),
      ),
    );
  }

  /// Build the main content (icon, title, message, action)
  Widget _buildContent(BuildContext context, Color effectiveColor, {
    required bool isCenter,
    bool isChart = false,
  }) {
    final iconSize = isChart ? 32.0 : (isCenter ? 64.0 : 48.0);
    final titleStyle = isChart
      ? Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)
      : (isCenter
          ? Theme.of(context).textTheme.headlineSmall?.copyWith(color: Colors.grey.shade600)
          : Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold));

    final messageStyle = isChart
      ? Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600)
      : (isCenter
          ? Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade500)
          : TextStyle(fontSize: 14, color: Theme.of(context).colorScheme.onSurface.withAlpha(153)));

    return Column(
      mainAxisAlignment: isCenter ? MainAxisAlignment.center : MainAxisAlignment.start,
      mainAxisSize: isCenter ? MainAxisSize.max : MainAxisSize.min,
      children: [
        // Icon with background circle
        Container(
          padding: EdgeInsets.all(isChart ? 12 : 16),
          decoration: BoxDecoration(
            color: effectiveColor.withAlpha(26), // 0.1 * 255 = 26
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            size: iconSize,
            color: effectiveColor,
          ),
        ),
        SizedBox(height: isChart ? 12 : 16),

        // Title
        Text(
          title,
          style: titleStyle,
          textAlign: TextAlign.center,
        ),
        SizedBox(height: isChart ? 4 : 8),

        // Message
        Text(
          message,
          style: messageStyle,
          textAlign: TextAlign.center,
        ),

        // Action button
        if (action != null) ...[
          SizedBox(height: isChart ? 16 : 24),
          action!,
        ],
      ],
    );
  }

  /// Build animated content with fade-in effect
  Widget _buildAnimatedContent(Widget content) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 20 * (1 - value)),
            child: content,
          ),
        );
      },
    );
  }
}

/// Legacy compatibility - EmptyState class for backward compatibility
///
/// This maintains the existing API while internally using UniversalEmptyState.
/// Existing code using EmptyState will continue to work without changes.
class EmptyState extends StatelessWidget {
  final String title;
  final String message;
  final IconData icon;
  final Widget? action;

  const EmptyState({
    super.key,
    required this.title,
    required this.message,
    this.icon = Icons.inbox_outlined,
    this.action,
  });

  /// Factory constructor for custom empty state (backward compatibility)
  factory EmptyState.custom({
    required String title,
    required String message,
    IconData icon = Icons.inbox_outlined,
    Widget? action,
  }) {
    return EmptyState(
      title: title,
      message: message,
      icon: icon,
      action: action,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Delegate to UniversalEmptyState for consistent behavior
    return UniversalEmptyState(
      title: title,
      message: message,
      icon: icon,
      action: action,
      type: EmptyStateType.noData,
      layout: EmptyStateLayout.center,
    );
  }
}

/// Legacy compatibility - MilkEmptyState for backward compatibility
///
/// This maintains the existing API while internally using UniversalEmptyState.
/// Existing milk module code will continue to work without changes.
class MilkEmptyState extends StatelessWidget {
  final IconData icon;
  final String message;
  final String subtitle;
  final Color color;
  final Widget? action;

  const MilkEmptyState({
    super.key,
    required this.icon,
    required this.message,
    required this.subtitle,
    required this.color,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    // Delegate to UniversalEmptyState for consistent behavior
    return UniversalEmptyState(
      title: message,
      message: subtitle,
      icon: icon,
      color: color,
      action: action,
      type: EmptyStateType.noData,
      layout: EmptyStateLayout.center,
    );
  }
}

/// Legacy compatibility - ChartEmptyState for backward compatibility
///
/// This maintains the existing API while internally using UniversalEmptyState.
/// Existing chart code will continue to work without changes.
class ChartEmptyState extends StatelessWidget {
  final IconData icon;
  final String message;
  final String subtitle;
  final Color color;
  final double? height;
  final Widget? action;

  const ChartEmptyState({
    super.key,
    required this.icon,
    required this.message,
    required this.subtitle,
    required this.color,
    this.height,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    // Delegate to UniversalEmptyState for consistent behavior
    return UniversalEmptyState(
      title: message,
      message: subtitle,
      icon: icon,
      color: color,
      action: action,
      type: EmptyStateType.noData,
      layout: EmptyStateLayout.chart,
      height: height,
    );
  }
}

/// Utility class for creating common empty state action buttons
class EmptyStateActions {
  /// Create a standard "Add First Record" button
  static Widget addFirstRecord({
    required VoidCallback onPressed,
    String text = 'Add First Record',
    IconData icon = Icons.add,
    Color? backgroundColor,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Create a "Clear Filters" button
  static Widget clearFilters({
    required VoidCallback onPressed,
    String text = 'Clear Filters',
    IconData icon = Icons.filter_alt_off,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(text),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Create a "Retry" button for error states
  static Widget retry({
    required VoidCallback onPressed,
    String text = 'Retry',
    IconData icon = Icons.refresh,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: UniversalEmptyStateTheme.errorRed,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Create a custom action button
  static Widget custom({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    Color? backgroundColor,
    Color? foregroundColor,
    bool outlined = false,
  }) {
    if (outlined) {
      return OutlinedButton.icon(
        onPressed: onPressed,
        icon: icon != null ? Icon(icon) : const SizedBox.shrink(),
        label: Text(text),
        style: OutlinedButton.styleFrom(
          foregroundColor: foregroundColor,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    } else {
      return ElevatedButton.icon(
        onPressed: onPressed,
        icon: icon != null ? Icon(icon) : const SizedBox.shrink(),
        label: Text(text),
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor ?? Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }
}
