import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../../../services/database/database_helper.dart';
import '../../../utils/message_utils.dart';
import '../../widgets/index.dart'; // Import Universal Screen State System

class HealthScreen extends StatefulWidget {
  const HealthScreen({super.key});

  @override
  State<HealthScreen> createState() => _HealthScreenState();
}

class _HealthScreenState extends State<HealthScreen>
    with UniversalScreenState, UniversalDataLoader {
  late DatabaseHelper _dbHelper;
  int _cattleCount = 0;
  int _healthRecordsCount = 0;
  int _vaccinationsCount = 0;
  int _treatmentsCount = 0;
  int _activeRecordsCount = 0;
  int _completedRecordsCount = 0;

  @override
  void initState() {
    super.initState();
    _dbHelper = DatabaseHelper.instance;
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      final allCattle = await _dbHelper.cattleHandler.getAllCattle();
      final allHealthRecords =
          await _dbHelper.healthHandler.getAllHealthRecords();
      final allVaccinations =
          await _dbHelper.healthHandler.getAllVaccinations();
      final allTreatments = await _dbHelper.healthHandler.getAllTreatments();

      // Process statistics
      int activeRecords = 0;
      int completedRecords = 0;

      for (var record in allHealthRecords) {
        if (record.isResolved == false) {
          activeRecords++;
        } else if (record.isResolved == true) {
          completedRecords++;
        }
      }

      setState(() {
        _cattleCount = allCattle.length;
        _healthRecordsCount = allHealthRecords.length;
        _vaccinationsCount = allVaccinations.length;
        _treatmentsCount = allTreatments.length;
        _activeRecordsCount = activeRecords;
        _completedRecordsCount = completedRecords;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted && context.mounted) {
        HealthMessageUtils.showError(context, 'Error loading health data: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    const mainColor = Color(0xFF2E7D32);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Health Management',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: mainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart, color: Colors.white),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.healthReport,
            ),
            tooltip: 'View Health Reports',
          ),
        ],
      ),
      backgroundColor: Colors.grey[50],
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          // Fetch cattle data from database using DatabaseHelper
          final dbHelper = DatabaseHelper.instance;
          final allCattle = await dbHelper.cattleHandler.getAllCattle();

          if (!context.mounted) return;

          if (allCattle.isEmpty) {
            // Handle the empty case, e.g., show a message or return
            return;
          }

          // Get the first cattle's tag ID with null safety

          showDialog(
            context: context,
            builder: (context) => HealthRecordFormDialog(
              cattle: allCattle,
            ),
            // Handle the result when dialog is closed
          ).then((record) async {
            if (record != null) {
              // Handle saving the record
              await dbHelper.healthHandler.addOrUpdateHealthRecord(record);
              if (mounted) {
                _loadData(); // Refresh data
                if (context.mounted) {
                  HealthMessageUtils.showSuccess(context,
                      HealthMessageUtils.healthRecordCreated());
                }
              }
            }
          });
        },
        backgroundColor: mainColor,
        child: const Icon(Icons.add),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatisticsSection(),
                    const SizedBox(height: 24),
                    _buildMainOptionsGrid(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildStatisticsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Health Overview',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  'Cattle',
                  _cattleCount.toString(),
                  Icons.pets,
                  Colors.brown,
                ),
                _buildStatItem(
                  'Health Records',
                  _healthRecordsCount.toString(),
                  Icons.medical_services,
                  Colors.blue,
                ),
                _buildStatItem(
                  'Vaccinations',
                  _vaccinationsCount.toString(),
                  Icons.medication_liquid,
                  Colors.green,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  'Treatments',
                  _treatmentsCount.toString(),
                  Icons.healing,
                  Colors.orange,
                ),
                _buildStatItem(
                  'Active',
                  _activeRecordsCount.toString(),
                  Icons.warning_amber,
                  Colors.red,
                ),
                _buildStatItem(
                  'Completed',
                  _completedRecordsCount.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withAlpha(25),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 28,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildMainOptionsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildOptionCard(
          title: 'Health Records',
          icon: Icons.medical_services,
          color: Colors.blue,
          onTap: () {
            Navigator.pushNamed(context, '/health_records');
          },
        ),
        _buildOptionCard(
          title: 'Vaccinations',
          icon: Icons.medication_liquid,
          color: Colors.green,
          onTap: () {
            Navigator.pushNamed(context, '/vaccinations');
          },
        ),
        _buildOptionCard(
          title: 'Treatments',
          icon: Icons.healing,
          color: Colors.orange,
          onTap: () {
            Navigator.pushNamed(context, '/treatments');
          },
        ),
        _buildOptionCard(
          title: 'Health Reports',
          icon: Icons.summarize,
          color: Colors.purple,
          onTap: () {
            // Health reports screen
          },
        ),
      ],
    );
  }

  Widget _buildOptionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withAlpha(25),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 40,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
