/// Universal Widget System Components
///
/// This package provides complete, universal widget systems for consistent
/// UI patterns across all modules in the Cattle Manager App.

// Core widgets
export 'app_drawer.dart';

// Universal Filter System
export 'filters/date_range/index.dart';
export 'filters/controllers/filter_controller.dart';
export 'filters/config/filter_config.dart';
export 'filters/config/module_configs.dart';
export 'filters/search_widget.dart';
export 'filters/sort_widget.dart';
export 'filters/filter_status_bar.dart';
export 'filters/custom_filters/index.dart';
export 'filters/layouts/index.dart';

// Universal Record Card System
export 'universal_record_card.dart';

// Universal Empty State System
export 'empty_state.dart';

// Universal Form System
export 'dialogs/standard_form_dialog.dart';
export 'dialogs/standard_form_builder.dart';
