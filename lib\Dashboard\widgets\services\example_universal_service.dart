import 'universal_service_patterns.dart';

/// Example Universal Service Implementation
/// 
/// This demonstrates how to implement a service using the Universal Service patterns.
/// This can serve as a template for migrating existing services.

/// Example data model
class ExampleRecord {
  final String? id;
  final String name;
  final String description;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ExampleRecord({
    this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  ExampleRecord copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExampleRecord(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory ExampleRecord.fromMap(Map<String, dynamic> map) {
    return ExampleRecord(
      id: map['id'],
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// Example Universal Service Implementation
class ExampleUniversalService extends UniversalService<ExampleRecord>
    with CrudServiceMixin<ExampleRecord>, ValidationServiceMixin<ExampleRecord> {
  
  // Mock database storage
  final Map<String, ExampleRecord> _storage = {};

  ExampleUniversalService() : super('ExampleService');

  @override
  String get serviceName => 'ExampleService';

  @override
  Duration get cacheDuration => const Duration(minutes: 10);

  @override
  bool get isCachingEnabled => true;

  /// CRUD Implementation

  @override
  Future<String> performCreate(ExampleRecord item) async {
    // Simulate database operation
    await Future.delayed(const Duration(milliseconds: 100));
    
    final id = generateBusinessId('EX');
    final now = DateTime.now();
    final recordWithId = item.copyWith(
      id: id,
      createdAt: now,
      updatedAt: now,
    );
    
    _storage[id] = recordWithId;
    
    logOperation('Record created', details: {'id': id, 'name': item.name});
    return id;
  }

  @override
  Future<ExampleRecord?> performRead(String id) async {
    // Simulate database operation
    await Future.delayed(const Duration(milliseconds: 50));
    
    final record = _storage[id];
    if (record == null) {
      throw ServiceError.notFound('Record not found: $id');
    }
    
    return record;
  }

  @override
  Future<void> performUpdate(String id, ExampleRecord item) async {
    // Simulate database operation
    await Future.delayed(const Duration(milliseconds: 100));
    
    final existing = _storage[id];
    if (existing == null) {
      throw ServiceError.notFound('Record not found: $id');
    }
    
    final updated = item.copyWith(
      id: id,
      createdAt: existing.createdAt,
      updatedAt: DateTime.now(),
    );
    
    _storage[id] = updated;
    
    logOperation('Record updated', details: {'id': id, 'name': item.name});
  }

  @override
  Future<void> performDelete(String id) async {
    // Simulate database operation
    await Future.delayed(const Duration(milliseconds: 100));
    
    final existing = _storage[id];
    if (existing == null) {
      throw ServiceError.notFound('Record not found: $id');
    }
    
    _storage.remove(id);
    
    logOperation('Record deleted', details: {'id': id});
  }

  @override
  Future<List<ExampleRecord>> performList({
    int? limit,
    int? offset,
    Map<String, dynamic>? filters,
  }) async {
    // Simulate database operation
    await Future.delayed(const Duration(milliseconds: 200));
    
    var records = _storage.values.toList();
    
    // Apply filters
    if (filters != null) {
      if (filters['name'] != null) {
        final nameFilter = filters['name'] as String;
        records = records.where((r) => 
          r.name.toLowerCase().contains(nameFilter.toLowerCase())
        ).toList();
      }
    }
    
    // Sort by creation date (newest first)
    records.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    // Apply pagination
    if (offset != null) {
      records = records.skip(offset).toList();
    }
    if (limit != null) {
      records = records.take(limit).toList();
    }
    
    return records;
  }

  /// Validation Implementation

  @override
  Future<List<String>> performValidation(ExampleRecord item, {bool isUpdate = false}) async {
    final errors = <String>[];
    
    // Name validation
    if (item.name.trim().isEmpty) {
      errors.add('Name is required');
    } else if (item.name.length < 2) {
      errors.add('Name must be at least 2 characters long');
    } else if (item.name.length > 100) {
      errors.add('Name must be less than 100 characters');
    }
    
    // Description validation
    if (item.description.trim().isEmpty) {
      errors.add('Description is required');
    } else if (item.description.length > 500) {
      errors.add('Description must be less than 500 characters');
    }
    
    // Check for duplicate names (only for new records)
    if (!isUpdate) {
      final existingWithSameName = _storage.values
          .where((r) => r.name.toLowerCase() == item.name.toLowerCase())
          .isNotEmpty;
      
      if (existingWithSameName) {
        errors.add('A record with this name already exists');
      }
    }
    
    return errors;
  }

  /// Additional business logic methods

  /// Search records by name
  Future<ServiceResult<List<ExampleRecord>, ServiceError>> searchByName(String query) async {
    return executeOperation('searchByName', () async {
      return await performList(filters: {'name': query});
    }, context: {'query': query});
  }

  /// Get records created in the last N days
  Future<ServiceResult<List<ExampleRecord>, ServiceError>> getRecentRecords(int days) async {
    return executeOperation('getRecentRecords', () async {
      final cutoffDate = DateTime.now().subtract(Duration(days: days));
      final allRecords = await performList();
      
      return allRecords.where((r) => r.createdAt.isAfter(cutoffDate)).toList();
    }, context: {'days': days});
  }

  /// Bulk create records
  Future<ServiceResult<List<String>, ServiceError>> bulkCreate(List<ExampleRecord> items) async {
    return executeOperation('bulkCreate', () async {
      final ids = <String>[];
      
      for (final item in items) {
        // Validate each item
        final validationResult = await validate(item);
        if (validationResult.isFailure) {
          throw ServiceError.validation(
            'Validation failed for item: ${item.name}',
            details: validationResult.error?.details,
          );
        }
        
        // Create the item
        final id = await performCreate(item);
        ids.add(id);
      }
      
      return ids;
    }, context: {'count': items.length});
  }

  /// Get statistics
  Future<ServiceResult<Map<String, dynamic>, ServiceError>> getStatistics() async {
    return executeOperation('getStatistics', () async {
      final allRecords = await performList();
      
      return {
        'totalRecords': allRecords.length,
        'averageNameLength': allRecords.isEmpty 
            ? 0.0 
            : allRecords.map((r) => r.name.length).reduce((a, b) => a + b) / allRecords.length,
        'oldestRecord': allRecords.isEmpty 
            ? null 
            : allRecords.map((r) => r.createdAt).reduce((a, b) => a.isBefore(b) ? a : b),
        'newestRecord': allRecords.isEmpty 
            ? null 
            : allRecords.map((r) => r.createdAt).reduce((a, b) => a.isAfter(b) ? a : b),
      };
    });
  }
}

/// Usage Examples
class ExampleUsage {
  final ExampleUniversalService _service = ExampleUniversalService();

  /// Example of using the service with proper error handling
  Future<void> demonstrateUsage() async {
    // Create a record
    final createResult = await _service.create(ExampleRecord(
      name: 'Test Record',
      description: 'This is a test record',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ));

    createResult.fold(
      (id) => print('Record created with ID: $id'),
      (error) => print('Failed to create record: $error'),
    );

    // Read a record
    if (createResult.isSuccess) {
      final id = createResult.data!;
      final readResult = await _service.read(id);
      
      readResult.fold(
        (record) => print('Record found: ${record?.name}'),
        (error) => print('Failed to read record: $error'),
      );
    }

    // List records with error handling
    final listResult = await _service.list(limit: 10);
    
    if (listResult.isSuccess) {
      final records = listResult.data!;
      print('Found ${records.length} records');
    } else {
      print('Failed to list records: ${listResult.errorMessage}');
    }

    // Search records
    final searchResult = await _service.searchByName('test');
    searchResult.fold(
      (records) => print('Search found ${records.length} records'),
      (error) => print('Search failed: $error'),
    );

    // Get statistics
    final statsResult = await _service.getStatistics();
    statsResult.fold(
      (stats) => print('Statistics: $stats'),
      (error) => print('Failed to get statistics: $error'),
    );
  }

  void dispose() {
    _service.dispose();
  }
}
