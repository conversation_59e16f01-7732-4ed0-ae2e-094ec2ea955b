import 'package:flutter/material.dart';
import '../empty_state.dart';

/// Universal Refresh Indicators
/// 
/// Provides consistent refresh indicators and controls across all modules
/// with module-specific theming and standardized behavior.

/// Universal Refresh Button
/// 
/// Consistent refresh button with loading state and module theming.
class UniversalRefreshButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isLoading;
  final Color? color;
  final double size;
  final String? tooltip;

  const UniversalRefreshButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
    this.color,
    this.size = 24.0,
    this.tooltip,
  });

  /// Module-specific refresh buttons
  factory UniversalRefreshButton.weight({
    VoidCallback? onPressed,
    bool isLoading = false,
    String? tooltip,
  }) {
    return UniversalRefreshButton(
      onPressed: onPressed,
      isLoading: isLoading,
      color: UniversalEmptyStateTheme.weight,
      tooltip: tooltip ?? 'Refresh weight data',
    );
  }

  factory UniversalRefreshButton.health({
    VoidCallback? onPressed,
    bool isLoading = false,
    String? tooltip,
  }) {
    return UniversalRefreshButton(
      onPressed: onPressed,
      isLoading: isLoading,
      color: UniversalEmptyStateTheme.health,
      tooltip: tooltip ?? 'Refresh health data',
    );
  }

  factory UniversalRefreshButton.milk({
    VoidCallback? onPressed,
    bool isLoading = false,
    String? tooltip,
  }) {
    return UniversalRefreshButton(
      onPressed: onPressed,
      isLoading: isLoading,
      color: UniversalEmptyStateTheme.milk,
      tooltip: tooltip ?? 'Refresh milk data',
    );
  }

  factory UniversalRefreshButton.breeding({
    VoidCallback? onPressed,
    bool isLoading = false,
    String? tooltip,
  }) {
    return UniversalRefreshButton(
      onPressed: onPressed,
      isLoading: isLoading,
      color: UniversalEmptyStateTheme.breeding,
      tooltip: tooltip ?? 'Refresh breeding data',
    );
  }

  factory UniversalRefreshButton.transactions({
    VoidCallback? onPressed,
    bool isLoading = false,
    String? tooltip,
  }) {
    return UniversalRefreshButton(
      onPressed: onPressed,
      isLoading: isLoading,
      color: UniversalEmptyStateTheme.transactions,
      tooltip: tooltip ?? 'Refresh transaction data',
    );
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: isLoading ? null : onPressed,
      icon: isLoading
          ? SizedBox(
              width: size,
              height: size,
              child: CircularProgressIndicator(
                strokeWidth: 2.0,
                valueColor: AlwaysStoppedAnimation<Color>(
                  color ?? Theme.of(context).primaryColor,
                ),
              ),
            )
          : Icon(
              Icons.refresh,
              size: size,
              color: color ?? Theme.of(context).primaryColor,
            ),
      tooltip: tooltip ?? 'Refresh',
    );
  }
}

/// Universal Refresh Status Indicator
/// 
/// Shows refresh status with last updated time and retry options.
class UniversalRefreshStatus extends StatelessWidget {
  final DateTime? lastUpdated;
  final bool isRefreshing;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final Color? color;

  const UniversalRefreshStatus({
    super.key,
    this.lastUpdated,
    this.isRefreshing = false,
    this.errorMessage,
    this.onRetry,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = color ?? theme.primaryColor;

    if (isRefreshing) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2.0,
                valueColor: AlwaysStoppedAnimation<Color>(effectiveColor),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Refreshing...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: effectiveColor,
              ),
            ),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 16,
              color: UniversalEmptyStateTheme.errorRed,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Refresh failed: $errorMessage',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: UniversalEmptyStateTheme.errorRed,
                ),
              ),
            ),
            if (onRetry != null) ...[
              const SizedBox(width: 8),
              TextButton(
                onPressed: onRetry,
                child: const Text('Retry'),
              ),
            ],
          ],
        ),
      );
    }

    if (lastUpdated != null) {
      final now = DateTime.now();
      final difference = now.difference(lastUpdated!);
      String timeAgo;

      if (difference.inMinutes < 1) {
        timeAgo = 'Just now';
      } else if (difference.inMinutes < 60) {
        timeAgo = '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        timeAgo = '${difference.inHours}h ago';
      } else {
        timeAgo = '${difference.inDays}d ago';
      }

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: Text(
          'Last updated $timeAgo',
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return const SizedBox.shrink();
  }
}

/// Universal Auto-Refresh Indicator
/// 
/// Shows when auto-refresh is enabled with countdown timer.
class UniversalAutoRefreshIndicator extends StatefulWidget {
  final Duration refreshInterval;
  final bool isEnabled;
  final VoidCallback? onToggle;
  final Color? color;

  const UniversalAutoRefreshIndicator({
    super.key,
    required this.refreshInterval,
    this.isEnabled = false,
    this.onToggle,
    this.color,
  });

  @override
  State<UniversalAutoRefreshIndicator> createState() => _UniversalAutoRefreshIndicatorState();
}

class _UniversalAutoRefreshIndicatorState extends State<UniversalAutoRefreshIndicator> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = widget.color ?? theme.primaryColor;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            widget.isEnabled ? Icons.autorenew : Icons.autorenew_outlined,
            size: 16,
            color: widget.isEnabled ? effectiveColor : Colors.grey.shade400,
          ),
          const SizedBox(width: 8),
          Text(
            widget.isEnabled
                ? 'Auto-refresh every ${_formatDuration(widget.refreshInterval)}'
                : 'Auto-refresh disabled',
            style: theme.textTheme.bodySmall?.copyWith(
              color: widget.isEnabled ? effectiveColor : Colors.grey.shade600,
            ),
          ),
          if (widget.onToggle != null) ...[
            const SizedBox(width: 8),
            Switch(
              value: widget.isEnabled,
              onChanged: (_) => widget.onToggle?.call(),
              activeColor: effectiveColor,
            ),
          ],
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m';
    } else if (duration.inHours < 24) {
      return '${duration.inHours}h';
    } else {
      return '${duration.inDays}d';
    }
  }
}

/// Universal Refresh Controls
/// 
/// Combines refresh button, status, and auto-refresh controls.
class UniversalRefreshControls extends StatelessWidget {
  final VoidCallback? onRefresh;
  final bool isRefreshing;
  final DateTime? lastUpdated;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final Duration? autoRefreshInterval;
  final bool isAutoRefreshEnabled;
  final VoidCallback? onToggleAutoRefresh;
  final Color? color;

  const UniversalRefreshControls({
    super.key,
    this.onRefresh,
    this.isRefreshing = false,
    this.lastUpdated,
    this.errorMessage,
    this.onRetry,
    this.autoRefreshInterval,
    this.isAutoRefreshEnabled = false,
    this.onToggleAutoRefresh,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Refresh status
        UniversalRefreshStatus(
          lastUpdated: lastUpdated,
          isRefreshing: isRefreshing,
          errorMessage: errorMessage,
          onRetry: onRetry,
          color: color,
        ),
        
        // Auto-refresh indicator
        if (autoRefreshInterval != null)
          UniversalAutoRefreshIndicator(
            refreshInterval: autoRefreshInterval!,
            isEnabled: isAutoRefreshEnabled,
            onToggle: onToggleAutoRefresh,
            color: color,
          ),
      ],
    );
  }
}
