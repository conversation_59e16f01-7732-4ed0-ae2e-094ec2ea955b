# Universal Empty State System

## Overview

The Universal Empty State System provides consistent empty state displays across all modules in the Cattle Manager App. It replaces all existing empty state implementations with a single, comprehensive widget system.

## Features

- **Module-specific theming** - Consistent colors matching each module
- **Multiple empty state types** - No data, no results, errors, network issues, etc.
- **Different layouts** - Center, card, inline, and chart layouts
- **Contextual actions** - Pre-built action buttons for common scenarios
- **Animations** - Smooth fade-in animations (optional)
- **Backward compatibility** - Existing code continues to work

## Quick Start

### Basic Usage

```dart
// Simple empty state
UniversalEmptyState(
  title: 'No Records',
  message: 'No records found.',
  icon: Icons.inbox_outlined,
)

// Module-specific empty state
UniversalEmptyState.weight(
  title: 'No Weight Records',
  message: 'Start by adding your first weight record.',
)
```

### With Actions

```dart
// With pre-built action button
UniversalEmptyState.milk(
  title: 'No Milk Records',
  message: 'Start tracking milk production.',
  action: EmptyStateActions.addFirstRecord(
    onPressed: () => showAddDialog(),
    backgroundColor: UniversalEmptyStateTheme.milk,
  ),
)

// With custom action
UniversalEmptyState.health(
  title: 'No Health Records',
  message: 'Add a health record to start tracking.',
  action: ElevatedButton.icon(
    onPressed: () => addHealthRecord(),
    icon: Icon(Icons.add),
    label: Text('Add Health Record'),
  ),
)
```

### Different Types and Layouts

```dart
// No results from search/filter
UniversalEmptyState.breeding(
  title: 'No Results Found',
  message: 'No records match your current filters.',
  type: EmptyStateType.noResults,
  action: EmptyStateActions.clearFilters(
    onPressed: () => clearFilters(),
  ),
)

// Chart empty state
UniversalEmptyState.transactions(
  title: 'No Data Available',
  message: 'Add transactions to see charts.',
  layout: EmptyStateLayout.chart,
  height: 200,
)

// Error state
UniversalEmptyState.error(
  title: 'Something Went Wrong',
  message: 'Unable to load data. Please try again.',
  action: EmptyStateActions.retry(
    onPressed: () => retryLoad(),
  ),
)
```

## Module-Specific Factory Constructors

Each module has its own factory constructor with appropriate theming:

- `UniversalEmptyState.weight()` - Blue theme, weight icon
- `UniversalEmptyState.health()` - Teal theme, health icon  
- `UniversalEmptyState.milk()` - Green theme, milk icon
- `UniversalEmptyState.breeding()` - Red theme, breeding icon
- `UniversalEmptyState.events()` - Purple theme, event icon
- `UniversalEmptyState.transactions()` - Orange theme, wallet icon

## Empty State Types

- `EmptyStateType.noData` - No data available at all (default)
- `EmptyStateType.noResults` - No results from search/filter
- `EmptyStateType.noNetwork` - Network connectivity issues
- `EmptyStateType.error` - General error state
- `EmptyStateType.noPermission` - Permission denied
- `EmptyStateType.maintenance` - Under maintenance

## Layouts

- `EmptyStateLayout.center` - Centered in available space (default)
- `EmptyStateLayout.card` - Card-style with background
- `EmptyStateLayout.inline` - Inline with minimal padding
- `EmptyStateLayout.chart` - Specialized for charts

## Pre-built Action Buttons

The `EmptyStateActions` class provides common action buttons:

```dart
// Add first record button
EmptyStateActions.addFirstRecord(
  onPressed: () => addRecord(),
  text: 'Add First Record', // optional
  backgroundColor: Colors.blue, // optional
)

// Clear filters button
EmptyStateActions.clearFilters(
  onPressed: () => clearFilters(),
)

// Retry button for errors
EmptyStateActions.retry(
  onPressed: () => retry(),
)

// Custom button
EmptyStateActions.custom(
  text: 'Custom Action',
  onPressed: () => doSomething(),
  icon: Icons.star,
  outlined: true, // for outlined style
)
```

## Migration Guide

### From EmptyState.custom()

**Before:**
```dart
EmptyState.custom(
  title: 'No Records',
  message: 'Add your first record.',
  icon: Icons.list,
  action: ElevatedButton(...),
)
```

**After:**
```dart
UniversalEmptyState.weight( // or appropriate module
  title: 'No Records',
  message: 'Add your first record.',
  action: EmptyStateActions.addFirstRecord(...),
)
```

### From MilkEmptyState

**Before:**
```dart
MilkEmptyState(
  icon: Icons.water_drop,
  message: 'No Milk Records',
  subtitle: 'Start tracking production.',
  color: Colors.green,
  action: ElevatedButton(...),
)
```

**After:**
```dart
UniversalEmptyState.milk(
  title: 'No Milk Records',
  message: 'Start tracking production.',
  action: EmptyStateActions.addFirstRecord(...),
)
```

### From Custom Implementations

**Before:**
```dart
Center(
  child: Column(
    children: [
      Icon(Icons.health_and_safety, size: 48),
      Text('No Health Records'),
      Text('Add a record to start tracking'),
      ElevatedButton(...),
    ],
  ),
)
```

**After:**
```dart
UniversalEmptyState.health(
  title: 'No Health Records',
  message: 'Add a record to start tracking.',
  action: EmptyStateActions.addFirstRecord(...),
)
```

## Backward Compatibility

Existing code using `EmptyState`, `MilkEmptyState`, and `ChartEmptyState` will continue to work without changes. These classes now internally use `UniversalEmptyState` for consistent behavior.

## Best Practices

1. **Use module-specific constructors** for consistent theming
2. **Choose appropriate empty state types** (noData vs noResults)
3. **Provide contextual actions** when possible
4. **Use pre-built action buttons** for consistency
5. **Consider the layout** based on the container (center vs card vs inline)

## Examples in Codebase

- Weight Records Tab: `lib/Dashboard/Weight/tabs/weight_records_tab.dart`
- Transactions List Tab: `lib/Dashboard/Transactions/transactions_tabs/transactions_list_tab.dart`

## Next Steps

This Universal Empty State System is part of Phase 1 of the Universal Components Development Plan. Next components to be implemented:

- Universal Screen State Management
- Universal Navigation Card System
- Universal Data Refresh System
