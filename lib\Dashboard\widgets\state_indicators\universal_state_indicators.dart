import 'package:flutter/material.dart';
import '../empty_state.dart';

/// Universal State Indicators
/// 
/// Provides consistent loading, error, and empty state widgets
/// that work seamlessly with UniversalScreenState mixin.

/// Universal Loading Indicator
/// 
/// Consistent loading indicator with optional message and module theming.
class UniversalLoadingIndicator extends StatelessWidget {
  final String? message;
  final Color? color;
  final double size;
  final bool showMessage;

  const UniversalLoadingIndicator({
    super.key,
    this.message,
    this.color,
    this.size = 40.0,
    this.showMessage = true,
  });

  /// Module-specific loading indicators
  factory UniversalLoadingIndicator.weight({String? message}) {
    return UniversalLoadingIndicator(
      message: message ?? 'Loading weight data...',
      color: UniversalEmptyStateTheme.weight,
    );
  }

  factory UniversalLoadingIndicator.health({String? message}) {
    return UniversalLoadingIndicator(
      message: message ?? 'Loading health data...',
      color: UniversalEmptyStateTheme.health,
    );
  }

  factory UniversalLoadingIndicator.milk({String? message}) {
    return UniversalLoadingIndicator(
      message: message ?? 'Loading milk data...',
      color: UniversalEmptyStateTheme.milk,
    );
  }

  factory UniversalLoadingIndicator.breeding({String? message}) {
    return UniversalLoadingIndicator(
      message: message ?? 'Loading breeding data...',
      color: UniversalEmptyStateTheme.breeding,
    );
  }

  factory UniversalLoadingIndicator.transactions({String? message}) {
    return UniversalLoadingIndicator(
      message: message ?? 'Loading transaction data...',
      color: UniversalEmptyStateTheme.transactions,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? Theme.of(context).primaryColor,
              ),
              strokeWidth: 3.0,
            ),
          ),
          if (showMessage && message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Universal Error Indicator
/// 
/// Consistent error display with retry functionality and module theming.
class UniversalErrorIndicator extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;
  final Color? color;
  final IconData icon;

  const UniversalErrorIndicator({
    super.key,
    required this.title,
    required this.message,
    this.onRetry,
    this.color,
    this.icon = Icons.error_outline,
  });

  /// Module-specific error indicators
  factory UniversalErrorIndicator.weight({
    required String message,
    VoidCallback? onRetry,
  }) {
    return UniversalErrorIndicator(
      title: 'Weight Data Error',
      message: message,
      onRetry: onRetry,
      color: UniversalEmptyStateTheme.weight,
      icon: Icons.monitor_weight,
    );
  }

  factory UniversalErrorIndicator.health({
    required String message,
    VoidCallback? onRetry,
  }) {
    return UniversalErrorIndicator(
      title: 'Health Data Error',
      message: message,
      onRetry: onRetry,
      color: UniversalEmptyStateTheme.health,
      icon: Icons.health_and_safety_outlined,
    );
  }

  factory UniversalErrorIndicator.milk({
    required String message,
    VoidCallback? onRetry,
  }) {
    return UniversalErrorIndicator(
      title: 'Milk Data Error',
      message: message,
      onRetry: onRetry,
      color: UniversalEmptyStateTheme.milk,
      icon: Icons.water_drop_outlined,
    );
  }

  factory UniversalErrorIndicator.breeding({
    required String message,
    VoidCallback? onRetry,
  }) {
    return UniversalErrorIndicator(
      title: 'Breeding Data Error',
      message: message,
      onRetry: onRetry,
      color: UniversalEmptyStateTheme.breeding,
      icon: Icons.favorite_outline,
    );
  }

  factory UniversalErrorIndicator.transactions({
    required String message,
    VoidCallback? onRetry,
  }) {
    return UniversalErrorIndicator(
      title: 'Transaction Data Error',
      message: message,
      onRetry: onRetry,
      color: UniversalEmptyStateTheme.transactions,
      icon: Icons.account_balance_wallet_outlined,
    );
  }

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? UniversalEmptyStateTheme.errorRed;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: effectiveColor.withAlpha(26), // 0.1 * 255 = 26
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 48,
                color: effectiveColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: effectiveColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: effectiveColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Universal State Builder
/// 
/// Builds appropriate widgets based on screen state with consistent styling.
class UniversalStateBuilder extends StatelessWidget {
  final ScreenState state;
  final Widget child;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final Widget? emptyWidget;
  final Color? moduleColor;

  const UniversalStateBuilder({
    super.key,
    required this.state,
    required this.child,
    this.errorMessage,
    this.onRetry,
    this.loadingWidget,
    this.errorWidget,
    this.emptyWidget,
    this.moduleColor,
  });

  @override
  Widget build(BuildContext context) {
    switch (state) {
      case ScreenState.initial:
      case ScreenState.loading:
        return loadingWidget ?? 
          UniversalLoadingIndicator(color: moduleColor);
      
      case ScreenState.error:
        return errorWidget ?? 
          UniversalErrorIndicator(
            title: 'Error',
            message: errorMessage ?? 'An error occurred',
            onRetry: onRetry,
            color: moduleColor,
          );
      
      case ScreenState.empty:
        return emptyWidget ?? 
          UniversalEmptyState(
            title: 'No Data',
            message: 'No data available',
            color: moduleColor,
          );
      
      case ScreenState.loaded:
      case ScreenState.refreshing:
        return child;
    }
  }
}

/// Screen State Extensions
/// 
/// Convenient extensions for ScreenState enum.
extension ScreenStateExtensions on ScreenState {
  bool get isLoading => this == ScreenState.loading || this == ScreenState.initial;
  bool get isError => this == ScreenState.error;
  bool get isEmpty => this == ScreenState.empty;
  bool get isLoaded => this == ScreenState.loaded;
  bool get isRefreshing => this == ScreenState.refreshing;
  bool get canShowContent => isLoaded || isRefreshing;
}
