import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../empty_state.dart';

/// Universal Form Field Builder System
/// 
/// Provides standardized form field styling, validation, and responsive layouts
/// that work consistently across all modules with:
/// - Module-specific theming
/// - Consistent validation patterns
/// - Responsive layouts
/// - Accessibility support
/// - Standardized styling

/// Form field layout types
enum FormFieldLayout {
  standard,    // Standard vertical layout
  compact,     // Compact horizontal layout
  inline,      // Inline layout for small fields
  responsive,  // Responsive layout that adapts to screen size
}

/// Form field validation types
enum ValidationType {
  required,
  email,
  phone,
  number,
  decimal,
  date,
  url,
  custom,
}

/// Validation rule class
class ValidationRule {
  final ValidationType type;
  final String? message;
  final String? Function(String?)? customValidator;
  final dynamic min;
  final dynamic max;
  final int? minLength;
  final int? maxLength;
  final RegExp? pattern;

  const ValidationRule({
    required this.type,
    this.message,
    this.customValidator,
    this.min,
    this.max,
    this.minLength,
    this.maxLength,
    this.pattern,
  });

  factory ValidationRule.required([String? message]) {
    return ValidationRule(
      type: ValidationType.required,
      message: message ?? 'This field is required',
    );
  }

  factory ValidationRule.email([String? message]) {
    return ValidationRule(
      type: ValidationType.email,
      message: message ?? 'Please enter a valid email address',
      pattern: RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
    );
  }

  factory ValidationRule.phone([String? message]) {
    return ValidationRule(
      type: ValidationType.phone,
      message: message ?? 'Please enter a valid phone number',
      pattern: RegExp(r'^\+?[\d\s\-\(\)]+$'),
    );
  }

  factory ValidationRule.number({String? message, num? min, num? max}) {
    return ValidationRule(
      type: ValidationType.number,
      message: message ?? 'Please enter a valid number',
      min: min,
      max: max,
      pattern: RegExp(r'^\d+$'),
    );
  }

  factory ValidationRule.decimal({String? message, double? min, double? max}) {
    return ValidationRule(
      type: ValidationType.decimal,
      message: message ?? 'Please enter a valid decimal number',
      min: min,
      max: max,
      pattern: RegExp(r'^\d*\.?\d+$'),
    );
  }

  factory ValidationRule.length({String? message, int? min, int? max}) {
    return ValidationRule(
      type: ValidationType.custom,
      message: message ?? 'Invalid length',
      minLength: min,
      maxLength: max,
    );
  }

  factory ValidationRule.custom(String? Function(String?) validator, [String? message]) {
    return ValidationRule(
      type: ValidationType.custom,
      message: message,
      customValidator: validator,
    );
  }

  /// Validate a value against this rule
  String? validate(String? value) {
    switch (type) {
      case ValidationType.required:
        if (value == null || value.trim().isEmpty) {
          return message;
        }
        break;

      case ValidationType.email:
      case ValidationType.phone:
      case ValidationType.number:
      case ValidationType.decimal:
        if (value != null && value.isNotEmpty && pattern != null) {
          if (!pattern!.hasMatch(value)) {
            return message;
          }
        }
        break;

      case ValidationType.custom:
        if (customValidator != null) {
          return customValidator!(value);
        }
        break;

      default:
        break;
    }

    // Length validation
    if (value != null) {
      if (minLength != null && value.length < minLength!) {
        return message ?? 'Minimum length is $minLength characters';
      }
      if (maxLength != null && value.length > maxLength!) {
        return message ?? 'Maximum length is $maxLength characters';
      }
    }

    // Range validation for numbers
    if (value != null && value.isNotEmpty && (min != null || max != null)) {
      final numValue = double.tryParse(value);
      if (numValue != null) {
        if (min != null && numValue < min) {
          return message ?? 'Minimum value is $min';
        }
        if (max != null && numValue > max) {
          return message ?? 'Maximum value is $max';
        }
      }
    }

    return null;
  }
}

/// Universal Form Field Builder
/// 
/// Provides consistent form field styling and behavior across all modules.
class UniversalFormFieldBuilder {
  static const EdgeInsets _defaultPadding = EdgeInsets.symmetric(horizontal: 12, vertical: 8);
  static const BoxConstraints _defaultConstraints = BoxConstraints(maxHeight: 56);
  static const BorderRadius _defaultBorderRadius = BorderRadius.all(Radius.circular(8));

  /// Build a text field with universal styling
  static Widget buildTextField({
    required String label,
    String? hint,
    String? initialValue,
    TextEditingController? controller,
    List<ValidationRule>? validationRules,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    bool obscureText = false,
    bool enabled = true,
    int maxLines = 1,
    int? maxLength,
    Widget? prefixIcon,
    Widget? suffixIcon,
    VoidCallback? onTap,
    Function(String)? onChanged,
    Function(String)? onFieldSubmitted,
    FormFieldLayout layout = FormFieldLayout.standard,
    Color? moduleColor,
    bool showBorder = true,
    EdgeInsets? contentPadding,
    BoxConstraints? constraints,
  }) {
    return TextFormField(
      controller: controller,
      initialValue: controller == null ? initialValue : null,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      obscureText: obscureText,
      enabled: enabled,
      maxLines: maxLines,
      maxLength: maxLength,
      onTap: onTap,
      onChanged: onChanged,
      onFieldSubmitted: onFieldSubmitted,
      validator: validationRules != null ? (value) => _validateField(value, validationRules) : null,
      decoration: _buildInputDecoration(
        label: label,
        hint: hint,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        moduleColor: moduleColor,
        showBorder: showBorder,
        contentPadding: contentPadding,
        constraints: constraints,
      ),
    );
  }

  /// Build a dropdown field with universal styling
  static Widget buildDropdownField<T>({
    required String label,
    String? hint,
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required Function(T?) onChanged,
    List<ValidationRule>? validationRules,
    bool enabled = true,
    Widget? prefixIcon,
    Widget? suffixIcon,
    FormFieldLayout layout = FormFieldLayout.standard,
    Color? moduleColor,
    bool showBorder = true,
    EdgeInsets? contentPadding,
    BoxConstraints? constraints,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: enabled ? onChanged : null,
      validator: validationRules != null ? (value) => _validateDropdown(value, validationRules) : null,
      isExpanded: true,
      decoration: _buildInputDecoration(
        label: label,
        hint: hint,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        moduleColor: moduleColor,
        showBorder: showBorder,
        contentPadding: contentPadding,
        constraints: constraints,
      ),
    );
  }

  /// Build a date picker field with universal styling
  static Widget buildDateField({
    required String label,
    String? hint,
    required DateTime? value,
    required Function(DateTime?) onChanged,
    List<ValidationRule>? validationRules,
    DateTime? firstDate,
    DateTime? lastDate,
    bool enabled = true,
    Widget? prefixIcon,
    FormFieldLayout layout = FormFieldLayout.standard,
    Color? moduleColor,
    bool showBorder = true,
    EdgeInsets? contentPadding,
    BoxConstraints? constraints,
  }) {
    final controller = TextEditingController(
      text: value != null ? DateFormat('MMM dd, yyyy').format(value) : '',
    );

    return TextFormField(
      controller: controller,
      enabled: enabled,
      readOnly: true,
      onTap: enabled ? () async {
        // Note: Date picker would need BuildContext from parent widget
        // For now, just format the current value
        if (value != null) {
          controller.text = DateFormat('MMM dd, yyyy').format(value);
        }
      } : null,
      validator: validationRules != null ? (value) => _validateField(value, validationRules) : null,
      decoration: _buildInputDecoration(
        label: label,
        hint: hint,
        prefixIcon: prefixIcon ?? const Icon(Icons.calendar_today),
        moduleColor: moduleColor,
        showBorder: showBorder,
        contentPadding: contentPadding,
        constraints: constraints,
      ),
    );
  }

  /// Build a switch field with universal styling
  static Widget buildSwitchField({
    required String label,
    String? subtitle,
    required bool value,
    required Function(bool) onChanged,
    bool enabled = true,
    Widget? leading,
    FormFieldLayout layout = FormFieldLayout.standard,
    Color? moduleColor,
  }) {
    return SwitchListTile(
      title: Text(label),
      subtitle: subtitle != null ? Text(subtitle) : null,
      value: value,
      onChanged: enabled ? onChanged : null,
      secondary: leading,
      activeColor: moduleColor,
      contentPadding: EdgeInsets.zero,
    );
  }

  /// Build a slider field with universal styling
  static Widget buildSliderField({
    required String label,
    required double value,
    required Function(double) onChanged,
    required double min,
    required double max,
    int? divisions,
    String Function(double)? labelFormatter,
    bool enabled = true,
    FormFieldLayout layout = FormFieldLayout.standard,
    Color? moduleColor,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              labelFormatter?.call(value) ?? value.toStringAsFixed(1),
              style: TextStyle(
                fontSize: 14,
                color: moduleColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          onChanged: enabled ? onChanged : null,
          activeColor: moduleColor,
          inactiveColor: moduleColor?.withOpacity(0.3),
        ),
      ],
    );
  }

  /// Build a multi-select field with universal styling
  static Widget buildMultiSelectField<T>({
    required String label,
    String? hint,
    required List<T> selectedValues,
    required List<T> allValues,
    required String Function(T) itemLabel,
    required Function(List<T>) onChanged,
    List<ValidationRule>? validationRules,
    bool enabled = true,
    Widget? prefixIcon,
    FormFieldLayout layout = FormFieldLayout.standard,
    Color? moduleColor,
    bool showBorder = true,
    EdgeInsets? contentPadding,
    BoxConstraints? constraints,
  }) {
    final controller = TextEditingController(
      text: selectedValues.isEmpty
          ? ''
          : selectedValues.map(itemLabel).join(', '),
    );

    return TextFormField(
      controller: controller,
      enabled: enabled,
      readOnly: true,
      onTap: enabled ? () async {
        // Implementation would show a multi-select dialog
        // This is a simplified version
      } : null,
      validator: validationRules != null ? (value) => _validateField(value, validationRules) : null,
      decoration: _buildInputDecoration(
        label: label,
        hint: hint,
        prefixIcon: prefixIcon,
        suffixIcon: const Icon(Icons.arrow_drop_down),
        moduleColor: moduleColor,
        showBorder: showBorder,
        contentPadding: contentPadding,
        constraints: constraints,
      ),
    );
  }

  /// Build input decoration with consistent styling
  static InputDecoration _buildInputDecoration({
    required String label,
    String? hint,
    Widget? prefixIcon,
    Widget? suffixIcon,
    Color? moduleColor,
    bool showBorder = true,
    EdgeInsets? contentPadding,
    BoxConstraints? constraints,
  }) {
    final effectiveColor = moduleColor ?? Colors.blue;

    return InputDecoration(
      labelText: label,
      hintText: hint,
      prefixIcon: prefixIcon != null
          ? IconTheme(
              data: IconThemeData(color: effectiveColor),
              child: prefixIcon,
            )
          : null,
      suffixIcon: suffixIcon,
      contentPadding: contentPadding ?? _defaultPadding,
      constraints: constraints ?? _defaultConstraints,
      border: showBorder
          ? OutlineInputBorder(
              borderRadius: _defaultBorderRadius,
              borderSide: BorderSide(color: Colors.grey.shade300),
            )
          : InputBorder.none,
      enabledBorder: showBorder
          ? OutlineInputBorder(
              borderRadius: _defaultBorderRadius,
              borderSide: BorderSide(color: Colors.grey.shade300),
            )
          : InputBorder.none,
      focusedBorder: showBorder
          ? OutlineInputBorder(
              borderRadius: _defaultBorderRadius,
              borderSide: BorderSide(color: effectiveColor, width: 2),
            )
          : UnderlineInputBorder(
              borderSide: BorderSide(color: effectiveColor, width: 2),
            ),
      errorBorder: showBorder
          ? OutlineInputBorder(
              borderRadius: _defaultBorderRadius,
              borderSide: const BorderSide(color: Colors.red),
            )
          : const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.red),
            ),
      focusedErrorBorder: showBorder
          ? OutlineInputBorder(
              borderRadius: _defaultBorderRadius,
              borderSide: const BorderSide(color: Colors.red, width: 2),
            )
          : const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.red, width: 2),
            ),
      labelStyle: TextStyle(color: effectiveColor),
      floatingLabelStyle: TextStyle(color: effectiveColor),
    );
  }

  /// Validate field value against validation rules
  static String? _validateField(String? value, List<ValidationRule> rules) {
    for (final rule in rules) {
      final error = rule.validate(value);
      if (error != null) {
        return error;
      }
    }
    return null;
  }

  /// Validate dropdown value against validation rules
  static String? _validateDropdown<T>(T? value, List<ValidationRule> rules) {
    final stringValue = value?.toString();
    return _validateField(stringValue, rules);
  }
}

/// Module-specific form field builders
class ModuleFormFieldBuilders {
  /// Weight module form fields
  static Widget weightTextField({
    required String label,
    String? hint,
    String? initialValue,
    TextEditingController? controller,
    List<ValidationRule>? validationRules,
    TextInputType? keyboardType,
    Function(String)? onChanged,
    bool enabled = true,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return UniversalFormFieldBuilder.buildTextField(
      label: label,
      hint: hint,
      initialValue: initialValue,
      controller: controller,
      validationRules: validationRules,
      keyboardType: keyboardType,
      onChanged: onChanged,
      enabled: enabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      moduleColor: UniversalEmptyStateTheme.weight,
    );
  }

  /// Health module form fields
  static Widget healthTextField({
    required String label,
    String? hint,
    String? initialValue,
    TextEditingController? controller,
    List<ValidationRule>? validationRules,
    TextInputType? keyboardType,
    Function(String)? onChanged,
    bool enabled = true,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return UniversalFormFieldBuilder.buildTextField(
      label: label,
      hint: hint,
      initialValue: initialValue,
      controller: controller,
      validationRules: validationRules,
      keyboardType: keyboardType,
      onChanged: onChanged,
      enabled: enabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      moduleColor: UniversalEmptyStateTheme.health,
    );
  }

  /// Breeding module form fields
  static Widget breedingTextField({
    required String label,
    String? hint,
    String? initialValue,
    TextEditingController? controller,
    List<ValidationRule>? validationRules,
    TextInputType? keyboardType,
    Function(String)? onChanged,
    bool enabled = true,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return UniversalFormFieldBuilder.buildTextField(
      label: label,
      hint: hint,
      initialValue: initialValue,
      controller: controller,
      validationRules: validationRules,
      keyboardType: keyboardType,
      onChanged: onChanged,
      enabled: enabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      moduleColor: UniversalEmptyStateTheme.breeding,
    );
  }

  /// Milk module form fields
  static Widget milkTextField({
    required String label,
    String? hint,
    String? initialValue,
    TextEditingController? controller,
    List<ValidationRule>? validationRules,
    TextInputType? keyboardType,
    Function(String)? onChanged,
    bool enabled = true,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return UniversalFormFieldBuilder.buildTextField(
      label: label,
      hint: hint,
      initialValue: initialValue,
      controller: controller,
      validationRules: validationRules,
      keyboardType: keyboardType,
      onChanged: onChanged,
      enabled: enabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      moduleColor: UniversalEmptyStateTheme.milk,
    );
  }

  /// Transactions module form fields
  static Widget transactionsTextField({
    required String label,
    String? hint,
    String? initialValue,
    TextEditingController? controller,
    List<ValidationRule>? validationRules,
    TextInputType? keyboardType,
    Function(String)? onChanged,
    bool enabled = true,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return UniversalFormFieldBuilder.buildTextField(
      label: label,
      hint: hint,
      initialValue: initialValue,
      controller: controller,
      validationRules: validationRules,
      keyboardType: keyboardType,
      onChanged: onChanged,
      enabled: enabled,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      moduleColor: UniversalEmptyStateTheme.transactions,
    );
  }
}
