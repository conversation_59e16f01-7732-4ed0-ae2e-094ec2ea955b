import 'package:flutter/material.dart';
import 'empty_state.dart';
import 'state_indicators/universal_state_indicators.dart';
import 'mixins/universal_screen_state.dart';

/// Universal List Builder System
/// 
/// Provides consistent list building patterns across all modules with:
/// - Built-in loading, error, and empty state handling
/// - Pull-to-refresh support
/// - Module-specific theming
/// - Flexible item builders
/// - Search and filter integration

/// List builder layout types
enum ListBuilderLayout {
  list,           // Standard ListView
  grid,           // GridView
  separated,      // ListView.separated
  builder,        // ListView.builder
  sliverList,     // SliverList
  sliverGrid,     // SliverGrid
}

/// Universal List Builder Widget
/// 
/// Replaces all existing _buildEventList, _buildRecordsList methods
/// with a single, comprehensive widget that handles all list scenarios.
class UniversalListBuilder<T> extends StatelessWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final ScreenState state;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final Future<void> Function()? onRefresh;
  final Widget? emptyStateWidget;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final ListBuilderLayout layout;
  final EdgeInsets? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final Widget Function(BuildContext, int)? separatorBuilder;
  final int? gridCrossAxisCount;
  final double? gridCrossAxisSpacing;
  final double? gridMainAxisSpacing;
  final double? gridChildAspectRatio;
  final Color? moduleColor;
  final String? semanticsLabel;

  const UniversalListBuilder({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.state = ScreenState.loaded,
    this.errorMessage,
    this.onRetry,
    this.onRefresh,
    this.emptyStateWidget,
    this.loadingWidget,
    this.errorWidget,
    this.layout = ListBuilderLayout.builder,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.separatorBuilder,
    this.gridCrossAxisCount = 2,
    this.gridCrossAxisSpacing = 8.0,
    this.gridMainAxisSpacing = 8.0,
    this.gridChildAspectRatio = 1.0,
    this.moduleColor,
    this.semanticsLabel,
  });

  /// Factory constructors for common scenarios

  /// Weight module list builder
  factory UniversalListBuilder.weight({
    required List<T> items,
    required Widget Function(BuildContext, T, int) itemBuilder,
    ScreenState state = ScreenState.loaded,
    String? errorMessage,
    VoidCallback? onRetry,
    Future<void> Function()? onRefresh,
    Widget? emptyStateWidget,
    ListBuilderLayout layout = ListBuilderLayout.builder,
    EdgeInsets? padding,
  }) {
    return UniversalListBuilder<T>(
      items: items,
      itemBuilder: itemBuilder,
      state: state,
      errorMessage: errorMessage,
      onRetry: onRetry,
      onRefresh: onRefresh,
      emptyStateWidget: emptyStateWidget,
      layout: layout,
      padding: padding,
      moduleColor: UniversalEmptyStateTheme.weight,
      semanticsLabel: 'Weight records list',
    );
  }

  /// Health module list builder
  factory UniversalListBuilder.health({
    required List<T> items,
    required Widget Function(BuildContext, T, int) itemBuilder,
    ScreenState state = ScreenState.loaded,
    String? errorMessage,
    VoidCallback? onRetry,
    Future<void> Function()? onRefresh,
    Widget? emptyStateWidget,
    ListBuilderLayout layout = ListBuilderLayout.builder,
    EdgeInsets? padding,
  }) {
    return UniversalListBuilder<T>(
      items: items,
      itemBuilder: itemBuilder,
      state: state,
      errorMessage: errorMessage,
      onRetry: onRetry,
      onRefresh: onRefresh,
      emptyStateWidget: emptyStateWidget,
      layout: layout,
      padding: padding,
      moduleColor: UniversalEmptyStateTheme.health,
      semanticsLabel: 'Health records list',
    );
  }

  /// Breeding module list builder
  factory UniversalListBuilder.breeding({
    required List<T> items,
    required Widget Function(BuildContext, T, int) itemBuilder,
    ScreenState state = ScreenState.loaded,
    String? errorMessage,
    VoidCallback? onRetry,
    Future<void> Function()? onRefresh,
    Widget? emptyStateWidget,
    ListBuilderLayout layout = ListBuilderLayout.builder,
    EdgeInsets? padding,
  }) {
    return UniversalListBuilder<T>(
      items: items,
      itemBuilder: itemBuilder,
      state: state,
      errorMessage: errorMessage,
      onRetry: onRetry,
      onRefresh: onRefresh,
      emptyStateWidget: emptyStateWidget,
      layout: layout,
      padding: padding,
      moduleColor: UniversalEmptyStateTheme.breeding,
      semanticsLabel: 'Breeding records list',
    );
  }

  /// Events module list builder
  factory UniversalListBuilder.events({
    required List<T> items,
    required Widget Function(BuildContext, T, int) itemBuilder,
    ScreenState state = ScreenState.loaded,
    String? errorMessage,
    VoidCallback? onRetry,
    Future<void> Function()? onRefresh,
    Widget? emptyStateWidget,
    ListBuilderLayout layout = ListBuilderLayout.builder,
    EdgeInsets? padding,
  }) {
    return UniversalListBuilder<T>(
      items: items,
      itemBuilder: itemBuilder,
      state: state,
      errorMessage: errorMessage,
      onRetry: onRetry,
      onRefresh: onRefresh,
      emptyStateWidget: emptyStateWidget,
      layout: layout,
      padding: padding,
      moduleColor: UniversalEmptyStateTheme.events,
      semanticsLabel: 'Events list',
    );
  }

  /// Transactions module list builder
  factory UniversalListBuilder.transactions({
    required List<T> items,
    required Widget Function(BuildContext, T, int) itemBuilder,
    ScreenState state = ScreenState.loaded,
    String? errorMessage,
    VoidCallback? onRetry,
    Future<void> Function()? onRefresh,
    Widget? emptyStateWidget,
    ListBuilderLayout layout = ListBuilderLayout.builder,
    EdgeInsets? padding,
  }) {
    return UniversalListBuilder<T>(
      items: items,
      itemBuilder: itemBuilder,
      state: state,
      errorMessage: errorMessage,
      onRetry: onRetry,
      onRefresh: onRefresh,
      emptyStateWidget: emptyStateWidget,
      layout: layout,
      padding: padding,
      moduleColor: UniversalEmptyStateTheme.transactions,
      semanticsLabel: 'Transactions list',
    );
  }

  @override
  Widget build(BuildContext context) {
    // Handle different states
    switch (state) {
      case ScreenState.initial:
      case ScreenState.loading:
        return _buildLoadingState();
      
      case ScreenState.error:
        return _buildErrorState();
      
      case ScreenState.empty:
        return _buildEmptyState();
      
      case ScreenState.loaded:
      case ScreenState.refreshing:
        if (items.isEmpty) {
          return _buildEmptyState();
        }
        return _buildList();
    }
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return loadingWidget ?? 
      UniversalLoadingIndicator(
        color: moduleColor,
        message: 'Loading...',
      );
  }

  /// Build error state
  Widget _buildErrorState() {
    return errorWidget ?? 
      UniversalErrorIndicator(
        title: 'Error',
        message: errorMessage ?? 'An error occurred',
        onRetry: onRetry,
        color: moduleColor,
      );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return emptyStateWidget ?? 
      UniversalEmptyState(
        title: 'No Items',
        message: 'No items to display',
        color: moduleColor,
      );
  }

  /// Build the actual list
  Widget _buildList() {
    Widget listWidget = _buildListWidget();

    // Wrap with RefreshIndicator if onRefresh is provided
    if (onRefresh != null) {
      listWidget = RefreshIndicator(
        onRefresh: onRefresh!,
        color: moduleColor,
        child: listWidget,
      );
    }

    return listWidget;
  }

  /// Build the appropriate list widget based on layout
  Widget _buildListWidget() {
    switch (layout) {
      case ListBuilderLayout.list:
        return _buildListView();
      case ListBuilderLayout.grid:
        return _buildGridView();
      case ListBuilderLayout.separated:
        return _buildSeparatedListView();
      case ListBuilderLayout.builder:
        return _buildBuilderListView();
      case ListBuilderLayout.sliverList:
        return _buildSliverList();
      case ListBuilderLayout.sliverGrid:
        return _buildSliverGrid();
    }
  }

  /// Build standard ListView
  Widget _buildListView() {
    return ListView(
      padding: padding ?? const EdgeInsets.all(8.0),
      physics: physics ?? const AlwaysScrollableScrollPhysics(),
      shrinkWrap: shrinkWrap,
      children: items.asMap().entries.map((entry) {
        return itemBuilder(context, entry.value, entry.key);
      }).toList(),
    );
  }

  /// Build ListView.builder
  Widget _buildBuilderListView() {
    return ListView.builder(
      padding: padding ?? const EdgeInsets.all(8.0),
      physics: physics ?? const AlwaysScrollableScrollPhysics(),
      shrinkWrap: shrinkWrap,
      itemCount: items.length,
      itemBuilder: (context, index) {
        return itemBuilder(context, items[index], index);
      },
    );
  }

  /// Build ListView.separated
  Widget _buildSeparatedListView() {
    return ListView.separated(
      padding: padding ?? const EdgeInsets.all(8.0),
      physics: physics ?? const AlwaysScrollableScrollPhysics(),
      shrinkWrap: shrinkWrap,
      itemCount: items.length,
      itemBuilder: (context, index) {
        return itemBuilder(context, items[index], index);
      },
      separatorBuilder: separatorBuilder ?? (context, index) => const Divider(),
    );
  }

  /// Build GridView
  Widget _buildGridView() {
    return GridView.builder(
      padding: padding ?? const EdgeInsets.all(8.0),
      physics: physics ?? const AlwaysScrollableScrollPhysics(),
      shrinkWrap: shrinkWrap,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: gridCrossAxisCount!,
        crossAxisSpacing: gridCrossAxisSpacing!,
        mainAxisSpacing: gridMainAxisSpacing!,
        childAspectRatio: gridChildAspectRatio!,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return itemBuilder(context, items[index], index);
      },
    );
  }

  /// Build SliverList
  Widget _buildSliverList() {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          return itemBuilder(context, items[index], index);
        },
        childCount: items.length,
      ),
    );
  }

  /// Build SliverGrid
  Widget _buildSliverGrid() {
    return SliverGrid(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: gridCrossAxisCount!,
        crossAxisSpacing: gridCrossAxisSpacing!,
        mainAxisSpacing: gridMainAxisSpacing!,
        childAspectRatio: gridChildAspectRatio!,
      ),
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          return itemBuilder(context, items[index], index);
        },
        childCount: items.length,
      ),
    );
  }
}

/// Universal List Builder with State Management
///
/// Combines UniversalListBuilder with automatic state management
/// for screens that need integrated state handling.
class UniversalStatefulListBuilder<T> extends StatefulWidget {
  final Future<List<T>> Function() dataLoader;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget? emptyStateWidget;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final ListBuilderLayout layout;
  final EdgeInsets? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final Widget Function(BuildContext, int)? separatorBuilder;
  final int? gridCrossAxisCount;
  final double? gridCrossAxisSpacing;
  final double? gridMainAxisSpacing;
  final double? gridChildAspectRatio;
  final Color? moduleColor;
  final String? semanticsLabel;
  final Duration? refreshInterval;

  const UniversalStatefulListBuilder({
    super.key,
    required this.dataLoader,
    required this.itemBuilder,
    this.emptyStateWidget,
    this.loadingWidget,
    this.errorWidget,
    this.layout = ListBuilderLayout.builder,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.separatorBuilder,
    this.gridCrossAxisCount = 2,
    this.gridCrossAxisSpacing = 8.0,
    this.gridMainAxisSpacing = 8.0,
    this.gridChildAspectRatio = 1.0,
    this.moduleColor,
    this.semanticsLabel,
    this.refreshInterval,
  });

  @override
  State<UniversalStatefulListBuilder<T>> createState() => _UniversalStatefulListBuilderState<T>();
}

class _UniversalStatefulListBuilderState<T> extends State<UniversalStatefulListBuilder<T>>
    with UniversalScreenState, UniversalDataRefresh {
  List<T> _items = [];

  @override
  void initState() {
    super.initState();

    // Enable periodic refresh if interval is provided
    if (widget.refreshInterval != null) {
      enablePeriodicRefresh(
        interval: widget.refreshInterval!,
        startImmediately: false,
      );
    }

    // Load initial data
    loadDataOnInit(_loadData);
  }

  @override
  void _performPeriodicRefresh() {
    if (mounted && !isLoading) {
      performRefresh(_loadData);
    }
  }

  Future<void> _loadData() async {
    final items = await widget.dataLoader();
    if (mounted) {
      safeSetState(() {
        _items = items;
      });
    }
  }

  Future<void> _handleRefresh() async {
    await performRefreshWithRetry(_loadData);
  }

  @override
  Widget build(BuildContext context) {
    return UniversalListBuilder<T>(
      items: _items,
      itemBuilder: widget.itemBuilder,
      state: screenState,
      errorMessage: errorMessage,
      onRetry: () => performRefresh(_loadData),
      onRefresh: _handleRefresh,
      emptyStateWidget: widget.emptyStateWidget,
      loadingWidget: widget.loadingWidget,
      errorWidget: widget.errorWidget,
      layout: widget.layout,
      padding: widget.padding,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      separatorBuilder: widget.separatorBuilder,
      gridCrossAxisCount: widget.gridCrossAxisCount,
      gridCrossAxisSpacing: widget.gridCrossAxisSpacing,
      gridMainAxisSpacing: widget.gridMainAxisSpacing,
      gridChildAspectRatio: widget.gridChildAspectRatio,
      moduleColor: widget.moduleColor,
      semanticsLabel: widget.semanticsLabel,
    );
  }
}
