import 'package:flutter/material.dart';

/// Universal Navigation Card System
/// 
/// Provides consistent navigation cards across all module screens with:
/// - Standardized styling and animations
/// - Module-specific theming
/// - Multiple layout options (list, grid, featured)
/// - Consistent interaction patterns
/// - Badge and notification support

/// Navigation card layout types
enum NavigationCardLayout {
  list,      // Full-width list item (default)
  grid,      // Grid item for 2-column layout
  featured,  // Large featured card
  compact,   // Compact version for dense layouts
}

/// Navigation card themes matching module colors
class NavigationCardTheme {
  // Module-specific colors matching UniversalEmptyStateTheme
  static const Color weight = Color(0xFF1976D2);       // Blue
  static const Color health = Color(0xFF00796B);       // Teal
  static const Color milk = Color(0xFF2E7D32);         // Green
  static const Color breeding = Color(0xFFD32F2F);     // Red
  static const Color events = Color(0xFF7B1FA2);       // Purple
  static const Color pregnancy = Color(0xFF9C27B0);    // Purple (distinct)
  static const Color delivery = Color(0xFF2E7D32);     // Green
  static const Color transactions = Color(0xFFFF8F00); // Orange
  static const Color cattle = Color(0xFF5D4037);       // Brown
  static const Color farmSetup = Color(0xFF455A64);    // Blue Grey
  static const Color reports = Color(0xFF6A1B9A);      // Deep Purple
  static const Color notifications = Color(0xFFE65100); // Deep Orange
}

/// Universal Navigation Card Widget
/// 
/// Replaces all existing _buildNavigationCard methods with a single,
/// comprehensive widget that handles all navigation card use cases.
class UniversalNavigationCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  final NavigationCardLayout layout;
  final String? badge;
  final Color? badgeColor;
  final bool enabled;
  final Widget? trailing;
  final double? elevation;
  final EdgeInsets? margin;
  final EdgeInsets? padding;

  const UniversalNavigationCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
    this.layout = NavigationCardLayout.list,
    this.badge,
    this.badgeColor,
    this.enabled = true,
    this.trailing,
    this.elevation,
    this.margin,
    this.padding,
  });

  /// Module-specific factory constructors
  
  /// Weight module navigation card
  factory UniversalNavigationCard.weight({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    NavigationCardLayout layout = NavigationCardLayout.list,
    String? badge,
    Widget? trailing,
  }) {
    return UniversalNavigationCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      color: NavigationCardTheme.weight,
      onTap: onTap,
      layout: layout,
      badge: badge,
      trailing: trailing,
    );
  }

  /// Health module navigation card
  factory UniversalNavigationCard.health({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    NavigationCardLayout layout = NavigationCardLayout.list,
    String? badge,
    Widget? trailing,
  }) {
    return UniversalNavigationCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      color: NavigationCardTheme.health,
      onTap: onTap,
      layout: layout,
      badge: badge,
      trailing: trailing,
    );
  }

  /// Milk module navigation card
  factory UniversalNavigationCard.milk({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    NavigationCardLayout layout = NavigationCardLayout.list,
    String? badge,
    Widget? trailing,
  }) {
    return UniversalNavigationCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      color: NavigationCardTheme.milk,
      onTap: onTap,
      layout: layout,
      badge: badge,
      trailing: trailing,
    );
  }

  /// Breeding module navigation card
  factory UniversalNavigationCard.breeding({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    NavigationCardLayout layout = NavigationCardLayout.list,
    String? badge,
    Widget? trailing,
  }) {
    return UniversalNavigationCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      color: NavigationCardTheme.breeding,
      onTap: onTap,
      layout: layout,
      badge: badge,
      trailing: trailing,
    );
  }

  /// Events module navigation card
  factory UniversalNavigationCard.events({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    NavigationCardLayout layout = NavigationCardLayout.list,
    String? badge,
    Widget? trailing,
  }) {
    return UniversalNavigationCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      color: NavigationCardTheme.events,
      onTap: onTap,
      layout: layout,
      badge: badge,
      trailing: trailing,
    );
  }

  /// Transactions module navigation card
  factory UniversalNavigationCard.transactions({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    NavigationCardLayout layout = NavigationCardLayout.list,
    String? badge,
    Widget? trailing,
  }) {
    return UniversalNavigationCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      color: NavigationCardTheme.transactions,
      onTap: onTap,
      layout: layout,
      badge: badge,
      trailing: trailing,
    );
  }

  /// Reports module navigation card
  factory UniversalNavigationCard.reports({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    NavigationCardLayout layout = NavigationCardLayout.list,
    String? badge,
    Widget? trailing,
  }) {
    return UniversalNavigationCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      color: NavigationCardTheme.reports,
      onTap: onTap,
      layout: layout,
      badge: badge,
      trailing: trailing,
    );
  }

  @override
  Widget build(BuildContext context) {
    switch (layout) {
      case NavigationCardLayout.list:
        return _buildListCard(context);
      case NavigationCardLayout.grid:
        return _buildGridCard(context);
      case NavigationCardLayout.featured:
        return _buildFeaturedCard(context);
      case NavigationCardLayout.compact:
        return _buildCompactCard(context);
    }
  }

  /// Build list layout card (replaces most _buildNavigationCard methods)
  Widget _buildListCard(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 16.0),
      child: Card(
        elevation: elevation ?? 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: enabled ? onTap : null,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Icon with background circle
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withAlpha(26), // 0.1 * 255 = 26
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Title and subtitle
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: enabled ? null : Colors.grey,
                              ),
                            ),
                          ),
                          if (badge != null) ...[
                            const SizedBox(width: 8),
                            _buildBadge(),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: enabled
                            ? Colors.grey.shade600
                            : Colors.grey.shade400,
                        ),
                      ),
                    ],
                  ),
                ),

                // Trailing widget or arrow
                if (trailing != null) ...[
                  const SizedBox(width: 8),
                  trailing!,
                ] else if (enabled) ...[
                  const SizedBox(width: 8),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey.shade400,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build grid layout card (for 2-column layouts like health screen)
  Widget _buildGridCard(BuildContext context) {
    return Card(
      elevation: elevation ?? 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: enabled ? onTap : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: padding ?? const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon with background circle
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withAlpha(26), // 0.1 * 255 = 26
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
              ),
              const SizedBox(height: 12),

              // Title with badge
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Flexible(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: enabled ? null : Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  if (badge != null) ...[
                    const SizedBox(width: 4),
                    _buildBadge(),
                  ],
                ],
              ),
              const SizedBox(height: 4),

              // Subtitle
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: enabled
                    ? Colors.grey.shade600
                    : Colors.grey.shade400,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              // Trailing widget
              if (trailing != null) ...[
                const SizedBox(height: 8),
                trailing!,
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build featured layout card (large, prominent display)
  Widget _buildFeaturedCard(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 16.0),
      child: Card(
        elevation: elevation ?? 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: InkWell(
          onTap: enabled ? onTap : null,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: padding ?? const EdgeInsets.all(24.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  color.withAlpha(26), // 0.1 * 255 = 26
                  color.withAlpha(13), // 0.05 * 255 = 13
                ],
              ),
            ),
            child: Row(
              children: [
                // Large icon
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: color.withAlpha(51), // 0.2 * 255 = 51
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 40,
                  ),
                ),
                const SizedBox(width: 20),

                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: enabled ? color : Colors.grey,
                              ),
                            ),
                          ),
                          if (badge != null) ...[
                            const SizedBox(width: 8),
                            _buildBadge(),
                          ],
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: enabled
                            ? Colors.grey.shade700
                            : Colors.grey.shade400,
                        ),
                      ),
                      if (trailing != null) ...[
                        const SizedBox(height: 12),
                        trailing!,
                      ],
                    ],
                  ),
                ),

                // Arrow
                if (enabled && trailing == null) ...[
                  const SizedBox(width: 12),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 20,
                    color: color,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build compact layout card (minimal space usage)
  Widget _buildCompactCard(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.only(bottom: 8.0),
      child: Card(
        elevation: elevation ?? 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: InkWell(
          onTap: enabled ? onTap : null,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(12.0),
            child: Row(
              children: [
                // Small icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withAlpha(26), // 0.1 * 255 = 26
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 12),

                // Title only (no subtitle in compact mode)
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          title,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: enabled ? null : Colors.grey,
                          ),
                        ),
                      ),
                      if (badge != null) ...[
                        const SizedBox(width: 4),
                        _buildBadge(),
                      ],
                    ],
                  ),
                ),

                // Trailing or arrow
                if (trailing != null) ...[
                  const SizedBox(width: 8),
                  trailing!,
                ] else if (enabled) ...[
                  const SizedBox(width: 8),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: Colors.grey.shade400,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build badge widget for notifications/counts
  Widget _buildBadge() {
    if (badge == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: badgeColor ?? color,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        badge!,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

/// Navigation Card Builder Utility
///
/// Provides helper methods for building common navigation card layouts.
class NavigationCardBuilder {
  /// Build a list of navigation cards for module screens
  static List<Widget> buildModuleCards({
    required List<NavigationCardData> cards,
    NavigationCardLayout layout = NavigationCardLayout.list,
  }) {
    return cards.map((data) => UniversalNavigationCard(
      title: data.title,
      subtitle: data.subtitle,
      icon: data.icon,
      color: data.color,
      onTap: data.onTap,
      layout: layout,
      badge: data.badge,
      trailing: data.trailing,
      enabled: data.enabled,
    )).toList();
  }

  /// Build a grid layout for navigation cards
  static Widget buildGrid({
    required List<NavigationCardData> cards,
    int crossAxisCount = 2,
    double crossAxisSpacing = 16,
    double mainAxisSpacing = 16,
  }) {
    return GridView.count(
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: cards.map((data) => UniversalNavigationCard(
        title: data.title,
        subtitle: data.subtitle,
        icon: data.icon,
        color: data.color,
        onTap: data.onTap,
        layout: NavigationCardLayout.grid,
        badge: data.badge,
        trailing: data.trailing,
        enabled: data.enabled,
      )).toList(),
    );
  }
}

/// Navigation Card Data Model
///
/// Data structure for navigation card information.
class NavigationCardData {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  final String? badge;
  final Widget? trailing;
  final bool enabled;

  const NavigationCardData({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
    this.badge,
    this.trailing,
    this.enabled = true,
  });
}
