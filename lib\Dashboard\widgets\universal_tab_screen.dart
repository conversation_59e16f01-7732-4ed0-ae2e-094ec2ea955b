import 'package:flutter/material.dart';
import '../../widgets/reusable_tab_bar.dart';
import '../../widgets/fab_styles.dart';
import 'mixins/universal_screen_state.dart';
import 'state_indicators/universal_state_indicators.dart';
import 'empty_state.dart';

/// Universal Tab Screen System
/// 
/// Provides complete tab screen management with:
/// - Automatic TabController management
/// - Built-in state management integration
/// - FAB positioning and tab-specific actions
/// - Module-specific theming
/// - Consistent error and loading states
/// - Refresh functionality

/// Tab screen configuration
class UniversalTabConfig {
  final String title;
  final List<TabItem> tabs;
  final List<Widget> tabViews;
  final List<Widget?> fabActions; // FAB for each tab (null = no FAB)
  final Color? moduleColor;
  final List<Widget>? appBarActions;
  final bool enableRefresh;
  final Future<void> Function()? onRefresh;
  final Widget? drawer;

  const UniversalTabConfig({
    required this.title,
    required this.tabs,
    required this.tabViews,
    required this.fabActions,
    this.moduleColor,
    this.appBarActions,
    this.enableRefresh = false,
    this.onRefresh,
    this.drawer,
  }) : assert(tabs.length == tabViews.length && tabs.length == fabActions.length,
              'tabs, tabViews, and fabActions must have the same length');
}

/// Universal Tab Screen Widget
/// 
/// Replaces all existing tab screen implementations with a single,
/// comprehensive widget that handles all tab screen scenarios.
class UniversalTabScreen extends StatefulWidget {
  final UniversalTabConfig config;
  final ScreenState? initialState;
  final String? errorMessage;
  final VoidCallback? onRetry;

  const UniversalTabScreen({
    super.key,
    required this.config,
    this.initialState,
    this.errorMessage,
    this.onRetry,
  });

  /// Factory constructors for common module patterns

  /// Weight module tab screen
  factory UniversalTabScreen.weight({
    required List<Widget> tabViews,
    List<Widget?>? fabActions,
    List<Widget>? appBarActions,
    Future<void> Function()? onRefresh,
    ScreenState? initialState,
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    final tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab2Label: 'Records',
      tab2Icon: Icons.list,
      tab3Label: 'Insights',
      tab3Icon: Icons.insights,
    );

    return UniversalTabScreen(
      config: UniversalTabConfig(
        title: 'Weight Management',
        tabs: tabs,
        tabViews: tabViews,
        fabActions: fabActions ?? [null, FabStyles.add(onPressed: () {}), null],
        moduleColor: UniversalEmptyStateTheme.weight,
        appBarActions: appBarActions,
        enableRefresh: onRefresh != null,
        onRefresh: onRefresh,
      ),
      initialState: initialState,
      errorMessage: errorMessage,
      onRetry: onRetry,
    );
  }

  /// Health module tab screen
  factory UniversalTabScreen.health({
    required List<Widget> tabViews,
    List<Widget?>? fabActions,
    List<Widget>? appBarActions,
    Future<void> Function()? onRefresh,
    ScreenState? initialState,
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    final tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Records',
      tab1Icon: Icons.health_and_safety,
      tab2Label: 'Analytics',
      tab2Icon: Icons.analytics,
      tab3Label: 'Calendar',
      tab3Icon: Icons.calendar_today,
    );

    return UniversalTabScreen(
      config: UniversalTabConfig(
        title: 'Health Management',
        tabs: tabs,
        tabViews: tabViews,
        fabActions: fabActions ?? [FabStyles.add(onPressed: () {}), null, null],
        moduleColor: UniversalEmptyStateTheme.health,
        appBarActions: appBarActions,
        enableRefresh: onRefresh != null,
        onRefresh: onRefresh,
      ),
      initialState: initialState,
      errorMessage: errorMessage,
      onRetry: onRetry,
    );
  }

  /// Breeding module tab screen
  factory UniversalTabScreen.breeding({
    required List<Widget> tabViews,
    List<Widget?>? fabActions,
    List<Widget>? appBarActions,
    Future<void> Function()? onRefresh,
    ScreenState? initialState,
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    final tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Records',
      tab1Icon: Icons.favorite,
      tab2Label: 'Calendar',
      tab2Icon: Icons.calendar_today,
      tab3Label: 'Analytics',
      tab3Icon: Icons.analytics,
    );

    return UniversalTabScreen(
      config: UniversalTabConfig(
        title: 'Breeding Management',
        tabs: tabs,
        tabViews: tabViews,
        fabActions: fabActions ?? [FabStyles.add(onPressed: () {}), null, null],
        moduleColor: UniversalEmptyStateTheme.breeding,
        appBarActions: appBarActions,
        enableRefresh: onRefresh != null,
        onRefresh: onRefresh,
      ),
      initialState: initialState,
      errorMessage: errorMessage,
      onRetry: onRetry,
    );
  }

  /// Milk module tab screen
  factory UniversalTabScreen.milk({
    required List<Widget> tabViews,
    List<Widget?>? fabActions,
    List<Widget>? appBarActions,
    Future<void> Function()? onRefresh,
    ScreenState? initialState,
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    final tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Records',
      tab1Icon: Icons.water_drop,
      tab2Label: 'Analytics',
      tab2Icon: Icons.analytics,
      tab3Label: 'Reports',
      tab3Icon: Icons.assessment,
    );

    return UniversalTabScreen(
      config: UniversalTabConfig(
        title: 'Milk Production',
        tabs: tabs,
        tabViews: tabViews,
        fabActions: fabActions ?? [FabStyles.add(onPressed: () {}), null, null],
        moduleColor: UniversalEmptyStateTheme.milk,
        appBarActions: appBarActions,
        enableRefresh: onRefresh != null,
        onRefresh: onRefresh,
      ),
      initialState: initialState,
      errorMessage: errorMessage,
      onRetry: onRetry,
    );
  }

  /// Transactions module tab screen
  factory UniversalTabScreen.transactions({
    required List<Widget> tabViews,
    List<Widget?>? fabActions,
    List<Widget>? appBarActions,
    Future<void> Function()? onRefresh,
    ScreenState? initialState,
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    final tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Records',
      tab1Icon: Icons.account_balance_wallet,
      tab2Label: 'Analytics',
      tab2Icon: Icons.analytics,
      tab3Label: 'Reports',
      tab3Icon: Icons.assessment,
    );

    return UniversalTabScreen(
      config: UniversalTabConfig(
        title: 'Financial Management',
        tabs: tabs,
        tabViews: tabViews,
        fabActions: fabActions ?? [FabStyles.add(onPressed: () {}), null, null],
        moduleColor: UniversalEmptyStateTheme.transactions,
        appBarActions: appBarActions,
        enableRefresh: onRefresh != null,
        onRefresh: onRefresh,
      ),
      initialState: initialState,
      errorMessage: errorMessage,
      onRetry: onRetry,
    );
  }

  @override
  State<UniversalTabScreen> createState() => _UniversalTabScreenState();
}

class _UniversalTabScreenState extends State<UniversalTabScreen>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    
    // Initialize tab controller
    _tabController = TabController(
      length: widget.config.tabs.length,
      vsync: this,
    );

    // Listen to tab changes for FAB updates
    _tabController.addListener(_onTabChanged);

    // Set initial state if provided
    if (widget.initialState != null) {
      _screenState = widget.initialState!;
      if (widget.errorMessage != null) {
        _errorMessage = widget.errorMessage;
      }
    }
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) return;
    
    safeSetState(() {
      _currentTabIndex = _tabController.index;
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: widget.config.drawer,
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// Build the app bar with module-specific styling
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.config.title),
      backgroundColor: widget.config.moduleColor,
      foregroundColor: Colors.white,
      actions: [
        // Refresh button if enabled
        if (widget.config.enableRefresh && widget.config.onRefresh != null)
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () async {
              setRefreshing();
              try {
                await widget.config.onRefresh!();
                setLoaded();
              } catch (e) {
                setError('Refresh failed: $e');
              }
            },
            tooltip: 'Refresh',
          ),
        // Additional actions
        ...?widget.config.appBarActions,
      ],
    );
  }

  /// Build the main body with state management
  Widget _buildBody() {
    return Column(
      children: [
        // Tab Bar
        ReusableTabBar.controlled(
          controller: _tabController,
          tabs: widget.config.tabs,
        ),
        // Tab Views with state management
        Expanded(
          child: UniversalStateBuilder(
            state: screenState,
            errorMessage: errorMessage,
            onRetry: widget.onRetry ?? () => setLoaded(),
            moduleColor: widget.config.moduleColor,
            loadingWidget: UniversalLoadingIndicator(
              color: widget.config.moduleColor,
              message: 'Loading ${widget.config.title.toLowerCase()}...',
            ),
            errorWidget: UniversalErrorIndicator(
              title: 'Error Loading ${widget.config.title}',
              message: errorMessage ?? 'An error occurred',
              onRetry: widget.onRetry ?? () => setLoaded(),
              color: widget.config.moduleColor,
            ),
            child: TabBarView(
              controller: _tabController,
              children: widget.config.tabViews,
            ),
          ),
        ),
      ],
    );
  }

  /// Build the floating action button for the current tab
  Widget? _buildFloatingActionButton() {
    if (_currentTabIndex >= 0 && _currentTabIndex < widget.config.fabActions.length) {
      return widget.config.fabActions[_currentTabIndex];
    }
    return null;
  }
}

/// Universal Tab Screen Builder
///
/// Provides a builder pattern for creating tab screens with automatic
/// state management and data loading.
class UniversalTabScreenBuilder extends StatefulWidget {
  final UniversalTabConfig config;
  final Future<void> Function()? dataLoader;
  final Duration? refreshInterval;

  const UniversalTabScreenBuilder({
    super.key,
    required this.config,
    this.dataLoader,
    this.refreshInterval,
  });

  @override
  State<UniversalTabScreenBuilder> createState() => _UniversalTabScreenBuilderState();
}

class _UniversalTabScreenBuilderState extends State<UniversalTabScreenBuilder>
    with UniversalScreenState, UniversalDataRefresh, UniversalDataLoader {

  @override
  void initState() {
    super.initState();

    // Enable periodic refresh if interval is provided
    if (widget.refreshInterval != null) {
      enablePeriodicRefresh(
        interval: widget.refreshInterval!,
        startImmediately: false,
      );
    }

    // Load initial data if loader is provided
    if (widget.dataLoader != null) {
      loadDataOnInit(widget.dataLoader!);
    } else {
      setLoaded(); // No data to load, go directly to loaded state
    }
  }

  @override
  void _performPeriodicRefresh() {
    if (mounted && !isLoading && widget.dataLoader != null) {
      performRefresh(widget.dataLoader!);
    }
  }

  Future<void> _handleRefresh() async {
    if (widget.dataLoader != null) {
      await performRefreshWithRetry(widget.dataLoader!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return UniversalTabScreen(
      config: widget.config.copyWith(
        enableRefresh: widget.dataLoader != null,
        onRefresh: widget.dataLoader != null ? _handleRefresh : null,
      ),
      initialState: screenState,
      errorMessage: errorMessage,
      onRetry: widget.dataLoader != null
          ? () => performRefresh(widget.dataLoader!)
          : null,
    );
  }
}

/// Extension for UniversalTabConfig to support copying
extension UniversalTabConfigExtension on UniversalTabConfig {
  UniversalTabConfig copyWith({
    String? title,
    List<TabItem>? tabs,
    List<Widget>? tabViews,
    List<Widget?>? fabActions,
    Color? moduleColor,
    List<Widget>? appBarActions,
    bool? enableRefresh,
    Future<void> Function()? onRefresh,
    Widget? drawer,
  }) {
    return UniversalTabConfig(
      title: title ?? this.title,
      tabs: tabs ?? this.tabs,
      tabViews: tabViews ?? this.tabViews,
      fabActions: fabActions ?? this.fabActions,
      moduleColor: moduleColor ?? this.moduleColor,
      appBarActions: appBarActions ?? this.appBarActions,
      enableRefresh: enableRefresh ?? this.enableRefresh,
      onRefresh: onRefresh ?? this.onRefresh,
      drawer: drawer ?? this.drawer,
    );
  }
}
